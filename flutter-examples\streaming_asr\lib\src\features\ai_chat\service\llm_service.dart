// Copyright (c) 2024 VocalMind AI
import 'dart:convert';
import 'dart:async';

import '../../../core/api/api_client.dart';
import '../../../core/log/app_logger.dart';
import '../../../shared/model/app_error.dart';
import '../model/chat_models.dart';

/// LLM服务
/// 
/// 负责与LLM API进行交互，支持聊天对话和流式响应
class LLMService {
  static const String _tag = 'LLMService';
  
  final ApiClient _apiClient;
  
  LLMService({ApiClient? apiClient}) 
      : _apiClient = apiClient ?? ApiClient.instance;

  /// 发送聊天消息
  Future<String> sendChatMessage({
    required String message,
    required LLMConfig config,
    String? meetingContext,
    List<ChatMessage>? chatHistory,
  }) async {
    try {
      AppLogger.debug(_tag, 'Sending chat message: ${message.substring(0, message.length > 50 ? 50 : message.length)}...');
      
      // 构建消息列表
      final messages = _buildMessages(
        userMessage: message,
        meetingContext: meetingContext,
        chatHistory: chatHistory,
      );
      
      // 构建请求体
      final requestBody = {
        'provider': config.provider,
        'model': config.model,
        'messages': messages,
        'temperature': config.temperature,
        'max_tokens': config.maxTokens,
        if (config.topP != null) 'top_p': config.topP,
        if (config.frequencyPenalty != null) 'frequency_penalty': config.frequencyPenalty,
        if (config.presencePenalty != null) 'presence_penalty': config.presencePenalty,
      };
      
      // 发送请求
      final response = await _apiClient.post('/llm/chat', data: requestBody);
      
      if (response.isSuccess && response.data != null) {
        final content = response.data['content'] as String?;
        if (content != null) {
          AppLogger.info(_tag, 'Chat message sent successfully');
          return content;
        }
      }
      
      throw AppError.network('Invalid response format');
      
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Failed to send chat message', e, stackTrace);
      if (e is AppError) {
        rethrow;
      }
      throw AppError.network('Failed to send chat message: $e');
    }
  }

  /// 发送流式聊天消息
  Stream<String> sendChatMessageStream({
    required String message,
    required LLMConfig config,
    String? meetingContext,
    List<ChatMessage>? chatHistory,
  }) async* {
    try {
      AppLogger.debug(_tag, 'Sending streaming chat message');
      
      // 构建消息列表
      final messages = _buildMessages(
        userMessage: message,
        meetingContext: meetingContext,
        chatHistory: chatHistory,
      );
      
      // 构建请求体
      final requestBody = {
        'provider': config.provider,
        'model': config.model,
        'messages': messages,
        'temperature': config.temperature,
        'max_tokens': config.maxTokens,
        if (config.topP != null) 'top_p': config.topP,
        if (config.frequencyPenalty != null) 'frequency_penalty': config.frequencyPenalty,
        if (config.presencePenalty != null) 'presence_penalty': config.presencePenalty,
      };
      
      // 发送流式请求
      await for (final chunk in _apiClient.postStream('/llm/chat/stream', data: requestBody)) {
        try {
          // 解析SSE数据
          if (chunk.startsWith('data: ')) {
            final jsonStr = chunk.substring(6);
            if (jsonStr.trim() == '[DONE]') {
              break;
            }
            
            final data = jsonDecode(jsonStr);
            final type = data['type'] as String?;
            
            if (type == 'content') {
              final content = data['content'] as String?;
              if (content != null) {
                yield content;
              }
            } else if (type == 'error') {
              final error = data['error'] as String?;
              throw AppError.network(error ?? 'Streaming error');
            } else if (type == 'done') {
              break;
            }
          }
        } catch (e) {
          AppLogger.warning(_tag, 'Failed to parse streaming chunk: $chunk');
          // 继续处理下一个chunk
        }
      }
      
      AppLogger.info(_tag, 'Streaming chat completed');
      
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Failed to send streaming chat message', e, stackTrace);
      if (e is AppError) {
        rethrow;
      }
      throw AppError.network('Failed to send streaming chat message: $e');
    }
  }

  /// 生成会议摘要
  Future<String> generateMeetingSummary({
    required String meetingContent,
    required LLMConfig config,
  }) async {
    try {
      AppLogger.debug(_tag, 'Generating meeting summary');
      
      final prompt = _buildSummaryPrompt(meetingContent);
      
      return await sendChatMessage(
        message: prompt,
        config: config,
      );
      
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Failed to generate meeting summary', e, stackTrace);
      rethrow;
    }
  }

  /// 生成会议标签
  Future<List<String>> generateMeetingTags({
    required String meetingContent,
    required LLMConfig config,
  }) async {
    try {
      AppLogger.debug(_tag, 'Generating meeting tags');
      
      final prompt = _buildTagsPrompt(meetingContent);
      
      final response = await sendChatMessage(
        message: prompt,
        config: config,
      );
      
      // 解析标签（假设返回格式为逗号分隔的标签）
      final tags = response
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();
      
      AppLogger.info(_tag, 'Generated ${tags.length} tags');
      return tags;
      
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Failed to generate meeting tags', e, stackTrace);
      rethrow;
    }
  }

  /// 构建消息列表
  List<Map<String, String>> _buildMessages({
    required String userMessage,
    String? meetingContext,
    List<ChatMessage>? chatHistory,
  }) {
    final messages = <Map<String, String>>[];
    
    // 添加系统提示词
    if (meetingContext != null && meetingContext.isNotEmpty) {
      messages.add({
        'role': 'system',
        'content': _buildSystemPrompt(meetingContext),
      });
    }
    
    // 添加聊天历史（最近10条）
    if (chatHistory != null && chatHistory.isNotEmpty) {
      final recentHistory = chatHistory.length > 10 
          ? chatHistory.sublist(chatHistory.length - 10)
          : chatHistory;
      
      for (final msg in recentHistory) {
        messages.add({
          'role': msg.isFromUser ? 'user' : 'assistant',
          'content': msg.content,
        });
      }
    }
    
    // 添加当前用户消息
    messages.add({
      'role': 'user',
      'content': userMessage,
    });
    
    return messages;
  }

  /// 构建系统提示词
  String _buildSystemPrompt(String meetingContext) {
    return '''你是一个专业的AI助手，专门帮助用户分析和理解会议内容。

会议内容：
$meetingContext

请基于以上会议内容回答用户的问题。你可以：
1. 总结会议的主要内容和要点
2. 回答关于会议内容的具体问题
3. 提取关键信息和行动项
4. 分析会议中的决策和结论
5. 提供相关的建议和见解

请保持回答的准确性和相关性，如果问题超出了会议内容的范围，请礼貌地说明。''';
  }

  /// 构建摘要提示词
  String _buildSummaryPrompt(String meetingContent) {
    return '''请为以下会议内容生成一个简洁而全面的摘要：

会议内容：
$meetingContent

请包括：
1. 会议的主要议题
2. 关键讨论点
3. 重要决策和结论
4. 行动项和后续步骤

摘要应该简洁明了，突出重点，长度控制在200-300字以内。''';
  }

  /// 构建标签提示词
  String _buildTagsPrompt(String meetingContent) {
    return '''请为以下会议内容生成5-8个相关的标签：

会议内容：
$meetingContent

标签要求：
1. 简洁明了，每个标签2-4个字
2. 能够准确反映会议的主题和内容
3. 便于分类和搜索
4. 使用中文

请只返回标签，用逗号分隔，不要包含其他内容。''';
  }
}
