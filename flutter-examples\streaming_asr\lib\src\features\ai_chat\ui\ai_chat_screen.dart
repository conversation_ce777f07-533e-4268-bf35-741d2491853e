// Copyright (c) 2024 VocalMind AI
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../controller/ai_chat_controller.dart';
import '../model/chat_models.dart';
import 'widgets/chat_message_widget.dart';
import 'widgets/chat_input_widget.dart';

/// AI聊天页面
class AiChatScreen extends ConsumerStatefulWidget {
  final String? meetingId;
  
  const AiChatScreen({
    super.key,
    this.meetingId,
  });

  @override
  ConsumerState<AiChatScreen> createState() => _AiChatScreenState();
}

class _AiChatScreenState extends ConsumerState<AiChatScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeChat();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  Future<void> _initializeChat() async {
    if (widget.meetingId != null && !_isInitialized) {
      await ref.read(aiChatControllerProvider.notifier)
          .initializeChatWithMeeting(widget.meetingId!);
      _isInitialized = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final chatState = ref.watch(aiChatControllerProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(chatState.currentSession?.title ?? 'AI 对话'),
        backgroundColor: theme.colorScheme.inversePrimary,
        actions: [
          // 生成摘要按钮
          if (widget.meetingId != null)
            IconButton(
              icon: const Icon(Icons.summarize),
              onPressed: chatState.isStreaming ? null : () {
                ref.read(aiChatControllerProvider.notifier).generateMeetingSummary();
              },
              tooltip: '生成会议摘要',
            ),
          // 清空聊天按钮
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: chatState.messages.isEmpty ? null : () {
              _showClearChatDialog(context);
            },
            tooltip: '清空聊天',
          ),
        ],
      ),
      body: Column(
        children: [
          // 会议上下文提示
          if (chatState.currentSession?.meetingRecordId != null)
            _buildContextBanner(theme),
          
          // 聊天消息列表
          Expanded(
            child: _buildMessagesList(chatState),
          ),
          
          // 输入框
          ChatInputWidget(
            controller: _messageController,
            onSend: _sendMessage,
            isEnabled: !chatState.isStreaming,
            isLoading: chatState.isStreaming,
          ),
        ],
      ),
    );
  }

  Widget _buildContextBanner(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withOpacity(0.3),
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '正在基于会议内容进行对话',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessagesList(AiChatState chatState) {
    if (chatState.messages.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: chatState.messages.length,
      itemBuilder: (context, index) {
        final message = chatState.messages[index];
        return ChatMessageWidget(
          message: message,
          onResend: message.status == ChatMessageStatus.error && message.isFromUser
              ? () => ref.read(aiChatControllerProvider.notifier).resendMessage(message)
              : null,
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '开始与AI对话',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.meetingId != null
                ? '您可以询问关于会议内容的任何问题'
                : '输入消息开始对话',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.meetingId != null) ...[
            const SizedBox(height: 24),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildSuggestedQuestion('总结一下这次会议的主要内容'),
                _buildSuggestedQuestion('有哪些重要的决策？'),
                _buildSuggestedQuestion('需要跟进的行动项有哪些？'),
                _buildSuggestedQuestion('会议中提到了哪些关键问题？'),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSuggestedQuestion(String question) {
    return ActionChip(
      label: Text(question),
      onPressed: () {
        _messageController.text = question;
        _sendMessage(question);
      },
    );
  }

  void _sendMessage(String message) {
    if (message.trim().isEmpty) return;
    
    ref.read(aiChatControllerProvider.notifier).sendMessage(message);
    _messageController.clear();
    
    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showClearChatDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空聊天记录'),
        content: const Text('确定要清空所有聊天记录吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(aiChatControllerProvider.notifier).clearChat();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('清空'),
          ),
        ],
      ),
    );
  }
}
