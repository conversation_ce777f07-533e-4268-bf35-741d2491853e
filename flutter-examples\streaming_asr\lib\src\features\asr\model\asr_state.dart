// Copyright (c)  2024  Xiaomi Corporation
import 'package:flutter/foundation.dart';

/// ASR系统的状态枚举
enum AsrStatus {
  /// 未初始化
  uninitialized,
  /// 已初始化，准备就绪
  ready,
  /// 正在录音和识别
  recording,
  /// 已停止
  stopped,
  /// 错误状态
  error,
}

/// ASR识别结果模型
@immutable
class AsrResult {
  const AsrResult({
    required this.text,
    required this.isFinal,
    required this.index,
  });

  /// 识别的文本内容
  final String text;
  
  /// 是否是最终结果
  final bool isFinal;
  
  /// 结果索引（用于区分不同的句子）
  final int index;

  AsrResult copyWith({
    String? text,
    bool? isFinal,
    int? index,
  }) {
    return AsrResult(
      text: text ?? this.text,
      isFinal: isFinal ?? this.isFinal,
      index: index ?? this.index,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AsrResult &&
        other.text == text &&
        other.isFinal == isFinal &&
        other.index == index;
  }

  @override
  int get hashCode => Object.hash(text, isFinal, index);

  @override
  String toString() => 'AsrResult(text: $text, isFinal: $isFinal, index: $index)';
}

/// ASR配置模型
@immutable
class AsrConfig {
  const AsrConfig({
    required this.sampleRate,
    required this.modelType,
    required this.numChannels,
  });

  /// 采样率
  final int sampleRate;
  
  /// 模型类型
  final int modelType;
  
  /// 声道数
  final int numChannels;

  static const AsrConfig defaultConfig = AsrConfig(
    sampleRate: 16000,
    modelType: 0,
    numChannels: 1,
  );

  AsrConfig copyWith({
    int? sampleRate,
    int? modelType,
    int? numChannels,
  }) {
    return AsrConfig(
      sampleRate: sampleRate ?? this.sampleRate,
      modelType: modelType ?? this.modelType,
      numChannels: numChannels ?? this.numChannels,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AsrConfig &&
        other.sampleRate == sampleRate &&
        other.modelType == modelType &&
        other.numChannels == numChannels;
  }

  @override
  int get hashCode => Object.hash(sampleRate, modelType, numChannels);

  @override
  String toString() => 'AsrConfig(sampleRate: $sampleRate, modelType: $modelType, numChannels: $numChannels)';
}

/// ASR整体状态类
@immutable
class AsrState {
  const AsrState({
    required this.status,
    required this.config,
    required this.currentResult,
    required this.finalizedResults,
    this.errorMessage,
  });

  /// 当前状态
  final AsrStatus status;
  
  /// ASR配置
  final AsrConfig config;
  
  /// 当前识别结果（可能是中间结果）
  final AsrResult currentResult;
  
  /// 已固化的结果列表
  final List<AsrResult> finalizedResults;
  
  /// 错误信息（如果有）
  final String? errorMessage;

  /// 初始状态
  static const AsrState initial = AsrState(
    status: AsrStatus.uninitialized,
    config: AsrConfig.defaultConfig,
    currentResult: AsrResult(text: '', isFinal: false, index: 0),
    finalizedResults: [],
  );

  /// 获取显示用的完整文本
  String get displayText {
    if (finalizedResults.isEmpty && currentResult.text.isEmpty) {
      return '';
    }
    
    final finalTexts = finalizedResults
        .map((result) => '${result.index}: ${result.text}')
        .join('\n');
    
    if (currentResult.text.isNotEmpty) {
      final currentText = '${currentResult.index}: ${currentResult.text}';
      return finalTexts.isEmpty ? currentText : '$currentText\n$finalTexts';
    }
    
    return finalTexts;
  }

  AsrState copyWith({
    AsrStatus? status,
    AsrConfig? config,
    AsrResult? currentResult,
    List<AsrResult>? finalizedResults,
    String? errorMessage,
  }) {
    return AsrState(
      status: status ?? this.status,
      config: config ?? this.config,
      currentResult: currentResult ?? this.currentResult,
      finalizedResults: finalizedResults ?? this.finalizedResults,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AsrState &&
        other.status == status &&
        other.config == config &&
        other.currentResult == currentResult &&
        listEquals(other.finalizedResults, finalizedResults) &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode => Object.hash(
        status,
        config,
        currentResult,
        Object.hashAll(finalizedResults),
        errorMessage,
      );

  @override
  String toString() => 'AsrState(status: $status, currentResult: $currentResult, finalizedResults: ${finalizedResults.length} items, errorMessage: $errorMessage)';
}
