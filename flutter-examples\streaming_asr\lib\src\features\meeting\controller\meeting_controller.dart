// Copyright (c) 2024 VocalMind AI
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/base/base_controller.dart';
import '../../../core/log/app_logger.dart';
import '../model/meeting_models.dart';
import '../service/meeting_database_service.dart';

/// 会议记录控制器
/// 
/// 管理会议记录的CRUD操作和状态
class MeetingController extends BaseListController<MeetingRecord> {
  final MeetingDatabaseService _databaseService;
  MeetingFilter _currentFilter = const MeetingFilter();
  
  MeetingController({
    MeetingDatabaseService? databaseService,
  }) : _databaseService = databaseService ?? MeetingDatabaseService.instance,
       super();

  @override
  String get tag => 'MeetingController';

  @override
  void onInit() {
    super.onInit();
    _initializeDatabase();
  }

  /// 初始化数据库
  Future<void> _initializeDatabase() async {
    await safeExecute(
      () async {
        await _databaseService.initialize();
        return await _loadMeetings();
      },
      errorMessage: 'Failed to initialize meeting database',
      onSuccess: (meetings) {
        AppLogger.info(tag, 'Meeting database initialized with ${meetings.length} records');
      },
      onError: (error) {
        AppLogger.error(tag, 'Failed to initialize meeting database: $error');
      },
    );
  }

  /// 加载会议记录列表
  Future<List<MeetingRecord>> _loadMeetings() async {
    return await _databaseService.getMeetings(filter: _currentFilter);
  }

  /// 刷新会议记录列表
  Future<void> refreshMeetings() async {
    await safeExecute(
      () async {
        return await _loadMeetings();
      },
      errorMessage: 'Failed to refresh meetings',
      onSuccess: (meetings) {
        AppLogger.debug(tag, 'Meetings refreshed: ${meetings.length} records');
      },
      onError: (error) {
        AppLogger.error(tag, 'Failed to refresh meetings: $error');
      },
    );
  }

  /// 创建新的会议记录
  Future<void> createMeeting({
    required String title,
    required String content,
    int? duration,
    int? wordCount,
    int? speakerCount,
    String? audioFilePath,
    List<String>? tags,
  }) async {
    await safeExecute(
      () async {
        final meeting = MeetingRecord.create(
          title: title,
          originalContent: content,
          duration: duration,
          wordCount: wordCount,
          speakerCount: speakerCount,
          audioFilePath: audioFilePath,
          tags: tags,
        );
        
        await _databaseService.insertMeeting(meeting);
        
        // 重新加载列表
        final updatedMeetings = await _loadMeetings();
        return updatedMeetings;
      },
      errorMessage: 'Failed to create meeting',
      onSuccess: (meetings) {
        AppLogger.info(tag, 'Meeting created successfully: $title');
      },
      onError: (error) {
        AppLogger.error(tag, 'Failed to create meeting: $error');
      },
    );
  }

  /// 更新会议记录
  Future<void> updateMeeting(MeetingRecord meeting) async {
    await safeExecute(
      () async {
        await _databaseService.updateMeeting(meeting);
        
        // 重新加载列表
        final updatedMeetings = await _loadMeetings();
        return updatedMeetings;
      },
      errorMessage: 'Failed to update meeting',
      onSuccess: (meetings) {
        AppLogger.info(tag, 'Meeting updated successfully: ${meeting.id}');
      },
      onError: (error) {
        AppLogger.error(tag, 'Failed to update meeting: $error');
      },
    );
  }

  /// 删除会议记录
  Future<void> deleteMeeting(String id) async {
    await safeExecute(
      () async {
        await _databaseService.deleteMeeting(id);
        
        // 重新加载列表
        final updatedMeetings = await _loadMeetings();
        return updatedMeetings;
      },
      errorMessage: 'Failed to delete meeting',
      onSuccess: (meetings) {
        AppLogger.info(tag, 'Meeting deleted successfully: $id');
      },
      onError: (error) {
        AppLogger.error(tag, 'Failed to delete meeting: $error');
      },
    );
  }

  /// 根据ID获取会议记录
  Future<MeetingRecord?> getMeetingById(String id) async {
    try {
      return await _databaseService.getMeetingById(id);
    } catch (e) {
      AppLogger.error(tag, 'Failed to get meeting by id: $id', e);
      return null;
    }
  }

  /// 应用过滤器
  Future<void> applyFilter(MeetingFilter filter) async {
    _currentFilter = filter;
    
    await safeExecute(
      () async {
        return await _loadMeetings();
      },
      errorMessage: 'Failed to apply filter',
      onSuccess: (meetings) {
        AppLogger.debug(tag, 'Filter applied: ${meetings.length} records found');
      },
      onError: (error) {
        AppLogger.error(tag, 'Failed to apply filter: $error');
      },
    );
  }

  /// 搜索会议记录
  Future<void> searchMeetings(String keyword) async {
    final filter = _currentFilter.copyWith(keyword: keyword);
    await applyFilter(filter);
  }

  /// 按状态过滤
  Future<void> filterByStatus(MeetingStatus? status) async {
    final filter = _currentFilter.copyWith(status: status);
    await applyFilter(filter);
  }

  /// 按日期范围过滤
  Future<void> filterByDateRange(DateTime? startDate, DateTime? endDate) async {
    final filter = _currentFilter.copyWith(
      startDate: startDate,
      endDate: endDate,
    );
    await applyFilter(filter);
  }

  /// 清除过滤器
  Future<void> clearFilter() async {
    await applyFilter(const MeetingFilter());
  }

  /// 获取会议记录统计信息
  Future<MeetingStatistics> getStatistics() async {
    try {
      final allMeetings = await _databaseService.getMeetings();
      
      int totalDuration = 0;
      int totalWordCount = 0;
      final statusCounts = <MeetingStatus, int>{};
      
      for (final meeting in allMeetings) {
        totalDuration += meeting.duration;
        totalWordCount += meeting.wordCount;
        statusCounts[meeting.status] = (statusCounts[meeting.status] ?? 0) + 1;
      }
      
      return MeetingStatistics(
        totalCount: allMeetings.length,
        totalDuration: totalDuration,
        totalWordCount: totalWordCount,
        statusCounts: statusCounts,
      );
    } catch (e) {
      AppLogger.error(tag, 'Failed to get statistics', e);
      return const MeetingStatistics(
        totalCount: 0,
        totalDuration: 0,
        totalWordCount: 0,
        statusCounts: {},
      );
    }
  }

  /// 获取当前过滤器
  MeetingFilter get currentFilter => _currentFilter;

  /// 检查是否有活跃的过滤器
  bool get hasActiveFilter => _currentFilter.hasActiveFilters;
}

/// 会议记录统计信息
class MeetingStatistics {
  final int totalCount;
  final int totalDuration;
  final int totalWordCount;
  final Map<MeetingStatus, int> statusCounts;

  const MeetingStatistics({
    required this.totalCount,
    required this.totalDuration,
    required this.totalWordCount,
    required this.statusCounts,
  });

  /// 格式化总时长
  String get formattedTotalDuration {
    if (totalDuration <= 0) return '0分钟';
    
    final hours = totalDuration ~/ 3600;
    final minutes = (totalDuration % 3600) ~/ 60;
    
    if (hours > 0) {
      return '${hours}小时${minutes}分钟';
    } else {
      return '${minutes}分钟';
    }
  }

  /// 格式化总字数
  String get formattedTotalWordCount {
    if (totalWordCount < 1000) {
      return '$totalWordCount字';
    } else if (totalWordCount < 10000) {
      return '${(totalWordCount / 1000).toStringAsFixed(1)}千字';
    } else {
      return '${(totalWordCount / 10000).toStringAsFixed(1)}万字';
    }
  }
}

/// 会议记录控制器Provider
final meetingControllerProvider = StateNotifierProvider<MeetingController, BaseStateData<List<MeetingRecord>>>((ref) {
  return MeetingController();
});

/// 会议记录列表Provider（便捷访问）
final meetingListProvider = Provider<List<MeetingRecord>>((ref) {
  return ref.watch(meetingControllerProvider).data ?? [];
});

/// 会议记录统计Provider
final meetingStatisticsProvider = FutureProvider<MeetingStatistics>((ref) async {
  final controller = ref.watch(meetingControllerProvider.notifier);
  return await controller.getStatistics();
});

/// 当前过滤器Provider
final currentMeetingFilterProvider = Provider<MeetingFilter>((ref) {
  return ref.watch(meetingControllerProvider.notifier).currentFilter;
});

/// 是否有活跃过滤器Provider
final hasActiveMeetingFilterProvider = Provider<bool>((ref) {
  return ref.watch(meetingControllerProvider.notifier).hasActiveFilter;
});
