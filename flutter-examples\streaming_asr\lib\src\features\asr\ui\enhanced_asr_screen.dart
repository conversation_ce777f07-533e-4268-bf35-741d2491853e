// Copyright (c) 2024 VocalMind AI
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/router/app_router.dart';
import '../../../shared/ui/custom_button.dart';
import '../../../shared/ui/loading_indicator.dart';
import '../controller/enhanced_asr_controller.dart';
import '../model/asr_state.dart';

/// 增强的ASR屏幕
/// 
/// 使用新的架构和UI组件
class EnhancedAsrScreen extends ConsumerStatefulWidget {
  const EnhancedAsrScreen({super.key});

  @override
  ConsumerState<EnhancedAsrScreen> createState() => _EnhancedAsrScreenState();
}

class _EnhancedAsrScreenState extends ConsumerState<EnhancedAsrScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final asrControllerState = ref.watch(enhancedAsrControllerProvider);
    final asrState = asrControllerState.data;
    final isRecording = ref.watch(isRecordingProvider);
    final fullText = ref.watch(fullTextProvider);
    final wordCount = ref.watch(wordCountProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('智能语音识别'),
        backgroundColor: theme.colorScheme.inversePrimary,
        actions: [
          // 设置按钮
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => context.go(AppRoutes.settings),
          ),
          // 历史记录按钮
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => context.go(AppRoutes.meetings),
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: asrControllerState.isLoading,
        child: Column(
          children: [
            // 状态指示器
            _buildStatusIndicator(theme, asrState),
            
            // 文本显示区域
            Expanded(
              child: _buildTextDisplay(theme, fullText, wordCount),
            ),
            
            // 控制按钮区域
            _buildControlButtons(theme, asrState, isRecording),
            
            // 底部功能按钮
            _buildBottomActions(theme, fullText),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(ThemeData theme, AsrState? asrState) {
    if (asrState == null) return const SizedBox.shrink();

    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (asrState.status) {
      case AsrStatus.uninitialized:
        statusColor = theme.colorScheme.outline;
        statusText = '未初始化';
        statusIcon = Icons.hourglass_empty;
        break;
      case AsrStatus.ready:
        statusColor = theme.colorScheme.primary;
        statusText = '准备就绪';
        statusIcon = Icons.mic_none;
        break;
      case AsrStatus.recording:
        statusColor = theme.colorScheme.error;
        statusText = '正在录音...';
        statusIcon = Icons.mic;
        break;
      case AsrStatus.stopped:
        statusColor = theme.colorScheme.secondary;
        statusText = '录音已停止';
        statusIcon = Icons.stop;
        break;
      case AsrStatus.error:
        statusColor = theme.colorScheme.error;
        statusText = '错误状态';
        statusIcon = Icons.error;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor),
          const SizedBox(width: 12),
          Text(
            statusText,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (asrState.status == AsrStatus.recording) ...[
            const Spacer(),
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(statusColor),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTextDisplay(ThemeData theme, String fullText, int wordCount) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和字数统计
          Row(
            children: [
              Text(
                '识别结果',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (wordCount > 0)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '$wordCount 字',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          
          // 文本内容
          Expanded(
            child: fullText.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.mic_none,
                          size: 64,
                          color: theme.colorScheme.outline,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          '点击开始录音\n开始您的语音识别之旅',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  )
                : Scrollbar(
                    controller: _scrollController,
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      child: SelectableText(
                        fullText,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          height: 1.6,
                        ),
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons(ThemeData theme, AsrState? asrState, bool isRecording) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 重置按钮
          CustomButton(
            text: '重置',
            icon: Icons.refresh,
            onPressed: asrState?.status != AsrStatus.uninitialized
                ? () => ref.read(enhancedAsrControllerProvider.notifier).resetAsr()
                : null,
            style: CustomButtonStyle.outline,
            size: CustomButtonSize.medium,
          ),
          
          // 主录音按钮
          SizedBox(
            width: 80,
            height: 80,
            child: FloatingActionButton(
              onPressed: _handleRecordingToggle,
              backgroundColor: isRecording 
                  ? theme.colorScheme.error 
                  : theme.colorScheme.primary,
              child: Icon(
                isRecording ? Icons.stop : Icons.mic,
                size: 32,
                color: Colors.white,
              ),
            ),
          ),
          
          // 初始化按钮
          CustomButton(
            text: '初始化',
            icon: Icons.settings_voice,
            onPressed: asrState?.status == AsrStatus.uninitialized
                ? () => ref.read(enhancedAsrControllerProvider.notifier).initializeAsr()
                : null,
            style: CustomButtonStyle.secondary,
            size: CustomButtonSize.medium,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions(ThemeData theme, String fullText) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // AI聊天按钮
          Expanded(
            child: CustomButton(
              text: 'AI聊天',
              icon: Icons.chat,
              onPressed: fullText.isNotEmpty
                  ? () => context.go(AppRoutes.aiChat)
                  : null,
              style: CustomButtonStyle.outline,
              size: CustomButtonSize.medium,
            ),
          ),
          const SizedBox(width: 12),
          
          // 生成待办按钮
          Expanded(
            child: CustomButton(
              text: '生成待办',
              icon: Icons.task_alt,
              onPressed: fullText.isNotEmpty
                  ? () => context.go(AppRoutes.todo)
                  : null,
              style: CustomButtonStyle.outline,
              size: CustomButtonSize.medium,
            ),
          ),
          const SizedBox(width: 12),
          
          // 保存按钮
          Expanded(
            child: CustomButton(
              text: '保存',
              icon: Icons.save,
              onPressed: fullText.isNotEmpty
                  ? _handleSave
                  : null,
              style: CustomButtonStyle.primary,
              size: CustomButtonSize.medium,
            ),
          ),
        ],
      ),
    );
  }

  void _handleRecordingToggle() {
    final controller = ref.read(enhancedAsrControllerProvider.notifier);
    
    if (controller.isRecording) {
      controller.stopRecording();
    } else if (controller.isReady) {
      controller.startRecording();
    } else {
      // 需要先初始化
      controller.initializeAsr().then((_) {
        if (controller.isReady) {
          controller.startRecording();
        }
      });
    }
  }

  void _handleSave() {
    final fullText = ref.read(fullTextProvider);
    if (fullText.isEmpty) return;

    // TODO: 实现保存到会议记录
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('保存功能开发中...'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
