// Copyright (c) 2024 VocalMind AI

/// 应用错误类型枚举
enum AppErrorType {
  network,
  authentication,
  authorization,
  validation,
  notFound,
  server,
  unknown,
  permission,
  storage,
  audio,
}

/// 应用错误模型
class AppError implements Exception {
  final AppErrorType type;
  final String message;
  final String? code;
  final dynamic originalError;
  final StackTrace? stackTrace;
  final Map<String, dynamic>? metadata;

  const AppError({
    required this.type,
    required this.message,
    this.code,
    this.originalError,
    this.stackTrace,
    this.metadata,
  });

  /// 创建网络错误
  factory AppError.network(String message, {String? code, dynamic originalError}) {
    return AppError(
      type: AppErrorType.network,
      message: message,
      code: code,
      originalError: originalError,
    );
  }

  /// 创建认证错误
  factory AppError.authentication(String message, {String? code}) {
    return AppError(
      type: AppErrorType.authentication,
      message: message,
      code: code,
    );
  }

  /// 创建权限错误
  factory AppError.authorization(String message, {String? code}) {
    return AppError(
      type: AppErrorType.authorization,
      message: message,
      code: code,
    );
  }

  /// 创建验证错误
  factory AppError.validation(String message, {String? code, Map<String, dynamic>? metadata}) {
    return AppError(
      type: AppErrorType.validation,
      message: message,
      code: code,
      metadata: metadata,
    );
  }

  /// 创建资源未找到错误
  factory AppError.notFound(String message, {String? code}) {
    return AppError(
      type: AppErrorType.notFound,
      message: message,
      code: code,
    );
  }

  /// 创建服务器错误
  factory AppError.server(String message, {String? code, dynamic originalError}) {
    return AppError(
      type: AppErrorType.server,
      message: message,
      code: code,
      originalError: originalError,
    );
  }

  /// 创建权限错误
  factory AppError.permission(String message, {String? code}) {
    return AppError(
      type: AppErrorType.permission,
      message: message,
      code: code,
    );
  }

  /// 创建存储错误
  factory AppError.storage(String message, {String? code, dynamic originalError}) {
    return AppError(
      type: AppErrorType.storage,
      message: message,
      code: code,
      originalError: originalError,
    );
  }

  /// 创建音频错误
  factory AppError.audio(String message, {String? code, dynamic originalError}) {
    return AppError(
      type: AppErrorType.audio,
      message: message,
      code: code,
      originalError: originalError,
    );
  }

  /// 创建未知错误
  factory AppError.unknown(String message, {dynamic originalError, StackTrace? stackTrace}) {
    return AppError(
      type: AppErrorType.unknown,
      message: message,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// 从异常创建错误
  factory AppError.fromException(Exception exception, {StackTrace? stackTrace}) {
    if (exception is AppError) {
      return exception;
    }

    return AppError.unknown(
      exception.toString(),
      originalError: exception,
      stackTrace: stackTrace,
    );
  }

  /// 获取用户友好的错误消息
  String get userMessage {
    switch (type) {
      case AppErrorType.network:
        return '网络连接失败，请检查网络设置';
      case AppErrorType.authentication:
        return '身份验证失败，请重新登录';
      case AppErrorType.authorization:
        return '权限不足，无法执行此操作';
      case AppErrorType.validation:
        return '输入信息有误，请检查后重试';
      case AppErrorType.notFound:
        return '请求的资源不存在';
      case AppErrorType.server:
        return '服务器错误，请稍后重试';
      case AppErrorType.permission:
        return '缺少必要权限，请在设置中开启';
      case AppErrorType.storage:
        return '存储空间不足或读写失败';
      case AppErrorType.audio:
        return '音频处理失败，请检查设备设置';
      case AppErrorType.unknown:
        return '未知错误，请联系技术支持';
    }
  }

  /// 检查是否是可重试的错误
  bool get isRetryable {
    switch (type) {
      case AppErrorType.network:
      case AppErrorType.server:
        return true;
      default:
        return false;
    }
  }

  /// 检查是否需要用户操作
  bool get requiresUserAction {
    switch (type) {
      case AppErrorType.authentication:
      case AppErrorType.authorization:
      case AppErrorType.validation:
      case AppErrorType.permission:
        return true;
      default:
        return false;
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'message': message,
      'code': code,
      'user_message': userMessage,
      'is_retryable': isRetryable,
      'requires_user_action': requiresUserAction,
      'metadata': metadata,
    };
  }

  /// 从JSON创建错误
  factory AppError.fromJson(Map<String, dynamic> json) {
    final typeString = json['type'] as String;
    final type = AppErrorType.values.firstWhere(
      (t) => t.name == typeString,
      orElse: () => AppErrorType.unknown,
    );

    return AppError(
      type: type,
      message: json['message'] as String,
      code: json['code'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.write('AppError(type: $type, message: $message');
    
    if (code != null) {
      buffer.write(', code: $code');
    }
    
    if (originalError != null) {
      buffer.write(', originalError: $originalError');
    }
    
    buffer.write(')');
    return buffer.toString();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppError &&
        other.type == type &&
        other.message == message &&
        other.code == code;
  }

  @override
  int get hashCode => Object.hash(type, message, code);
}

/// 错误结果包装类
class ErrorResult<T> {
  final T? data;
  final AppError? error;
  final bool isSuccess;

  const ErrorResult._({
    this.data,
    this.error,
    required this.isSuccess,
  });

  /// 创建成功结果
  factory ErrorResult.success(T data) {
    return ErrorResult._(
      data: data,
      isSuccess: true,
    );
  }

  /// 创建失败结果
  factory ErrorResult.failure(AppError error) {
    return ErrorResult._(
      error: error,
      isSuccess: false,
    );
  }

  /// 检查是否失败
  bool get isFailure => !isSuccess;

  /// 获取数据（如果成功）
  T get result {
    if (isSuccess && data != null) {
      return data!;
    }
    throw StateError('Cannot get result from failed ErrorResult');
  }

  /// 获取错误（如果失败）
  AppError get errorValue {
    if (isFailure && error != null) {
      return error!;
    }
    throw StateError('Cannot get error from successful ErrorResult');
  }

  /// 映射数据
  ErrorResult<R> map<R>(R Function(T) mapper) {
    if (isSuccess && data != null) {
      try {
        return ErrorResult.success(mapper(data!));
      } catch (e, stackTrace) {
        return ErrorResult.failure(
          AppError.unknown(
            'Mapping failed: $e',
            originalError: e,
            stackTrace: stackTrace,
          ),
        );
      }
    }
    return ErrorResult.failure(error!);
  }

  /// 异步映射数据
  Future<ErrorResult<R>> mapAsync<R>(Future<R> Function(T) mapper) async {
    if (isSuccess && data != null) {
      try {
        final result = await mapper(data!);
        return ErrorResult.success(result);
      } catch (e, stackTrace) {
        return ErrorResult.failure(
          AppError.unknown(
            'Async mapping failed: $e',
            originalError: e,
            stackTrace: stackTrace,
          ),
        );
      }
    }
    return ErrorResult.failure(error!);
  }

  @override
  String toString() {
    if (isSuccess) {
      return 'ErrorResult.success($data)';
    } else {
      return 'ErrorResult.failure($error)';
    }
  }
}
