// Copyright (c) 2024 VocalMind AI
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../core/utils/date_utils.dart' as app_date_utils;
import '../../model/chat_models.dart';

/// 聊天消息组件
class ChatMessageWidget extends StatelessWidget {
  final ChatMessage message;
  final VoidCallback? onResend;

  const ChatMessageWidget({
    super.key,
    required this.message,
    this.onResend,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isFromUser) ...[
            _buildAvatar(theme, false),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: message.isFromUser 
                  ? CrossAxisAlignment.end 
                  : CrossAxisAlignment.start,
              children: [
                _buildMessageBubble(theme),
                const SizedBox(height: 4),
                _buildMessageInfo(theme),
              ],
            ),
          ),
          if (message.isFromUser) ...[
            const SizedBox(width: 12),
            _buildAvatar(theme, true),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar(ThemeData theme, bool isUser) {
    return CircleAvatar(
      radius: 16,
      backgroundColor: isUser 
          ? theme.colorScheme.primary
          : theme.colorScheme.secondary,
      child: Icon(
        isUser ? Icons.person : Icons.smart_toy,
        size: 16,
        color: isUser 
            ? theme.colorScheme.onPrimary
            : theme.colorScheme.onSecondary,
      ),
    );
  }

  Widget _buildMessageBubble(ThemeData theme) {
    final isUser = message.isFromUser;
    final isError = message.status == ChatMessageStatus.error;
    final isSending = message.status == ChatMessageStatus.sending;
    
    return Container(
      constraints: const BoxConstraints(
        maxWidth: 300,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: _getBubbleColor(theme, isUser, isError),
        borderRadius: BorderRadius.circular(18).copyWith(
          bottomLeft: isUser ? const Radius.circular(18) : const Radius.circular(4),
          bottomRight: isUser ? const Radius.circular(4) : const Radius.circular(18),
        ),
        border: isError ? Border.all(
          color: theme.colorScheme.error.withOpacity(0.5),
          width: 1,
        ) : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (message.content.isNotEmpty)
            _buildMessageContent(theme, isUser, isError),
          
          if (isSending)
            _buildLoadingIndicator(theme),
          
          if (isError && onResend != null)
            _buildErrorActions(theme),
        ],
      ),
    );
  }

  Widget _buildMessageContent(ThemeData theme, bool isUser, bool isError) {
    final textColor = _getTextColor(theme, isUser, isError);
    
    return GestureDetector(
      onLongPress: () => _copyToClipboard(message.content),
      child: SelectableText(
        message.content,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: textColor,
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '正在输入...',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorActions(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            size: 16,
            color: theme.colorScheme.error,
          ),
          const SizedBox(width: 8),
          Text(
            '发送失败',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: onResend,
            child: Text(
              '重试',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInfo(ThemeData theme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          app_date_utils.DateUtils.formatTime(message.timestamp, format: 'HH:mm'),
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.6),
          ),
        ),
        if (message.isFromUser) ...[
          const SizedBox(width: 4),
          _buildMessageStatusIcon(theme),
        ],
      ],
    );
  }

  Widget _buildMessageStatusIcon(ThemeData theme) {
    switch (message.status) {
      case ChatMessageStatus.sending:
        return Icon(
          Icons.access_time,
          size: 12,
          color: theme.colorScheme.onSurfaceVariant.withOpacity(0.6),
        );
      case ChatMessageStatus.sent:
        return Icon(
          Icons.check,
          size: 12,
          color: theme.colorScheme.primary.withOpacity(0.6),
        );
      case ChatMessageStatus.error:
        return Icon(
          Icons.error_outline,
          size: 12,
          color: theme.colorScheme.error,
        );
    }
  }

  Color _getBubbleColor(ThemeData theme, bool isUser, bool isError) {
    if (isError) {
      return theme.colorScheme.errorContainer.withOpacity(0.3);
    }
    
    if (isUser) {
      return theme.colorScheme.primary;
    }
    
    return theme.colorScheme.surfaceVariant;
  }

  Color _getTextColor(ThemeData theme, bool isUser, bool isError) {
    if (isError) {
      return theme.colorScheme.onErrorContainer;
    }
    
    if (isUser) {
      return theme.colorScheme.onPrimary;
    }
    
    return theme.colorScheme.onSurfaceVariant;
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    // Note: In a real app, you might want to show a snackbar here
    // but we can't access the context from this widget
  }
}
