// Copyright (c) 2024 VocalMind AI
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../log/app_logger.dart';

/// 基础状态枚举
enum BaseState {
  initial,
  loading,
  success,
  error,
}

/// 基础状态数据类
class BaseStateData<T> {
  final BaseState state;
  final T? data;
  final String? error;
  final bool isLoading;
  final bool hasError;
  final bool hasData;

  BaseStateData({
    required this.state,
    this.data,
    this.error,
  }) : isLoading = state == BaseState.loading,
       hasError = state == BaseState.error,
       hasData = data != null;
  
  /// 创建初始状态
  factory BaseStateData.initial() {
    return BaseStateData(state: BaseState.initial);
  }

  /// 创建加载状态
  factory BaseStateData.loading([T? data]) {
    return BaseStateData(
      state: BaseState.loading,
      data: data,
    );
  }

  /// 创建成功状态
  factory BaseStateData.success(T data) {
    return BaseStateData(
      state: BaseState.success,
      data: data,
    );
  }

  /// 创建错误状态
  factory BaseStateData.error(String error, [T? data]) {
    return BaseStateData(
      state: BaseState.error,
      error: error,
      data: data,
    );
  }
  
  /// 复制状态并修改部分属性
  BaseStateData<T> copyWith({
    BaseState? state,
    T? data,
    String? error,
  }) {
    return BaseStateData(
      state: state ?? this.state,
      data: data ?? this.data,
      error: error ?? this.error,
    );
  }
  
  @override
  String toString() {
    return 'BaseStateData(state: $state, data: $data, error: $error)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BaseStateData<T> &&
        other.state == state &&
        other.data == data &&
        other.error == error;
  }
  
  @override
  int get hashCode => Object.hash(state, data, error);
}

/// 基础控制器抽象类
/// 
/// 提供通用的状态管理功能：
/// - 加载状态管理
/// - 错误处理
/// - 日志记录
/// - 生命周期管理
abstract class BaseController<T> extends StateNotifier<BaseStateData<T>> {
  BaseController() : super(BaseStateData.initial()) {
    _initialize();
  }
  
  /// 控制器标识，用于日志记录
  String get tag => runtimeType.toString();
  
  /// 初始化方法，子类可重写
  void _initialize() {
    AppLogger.debug(tag, 'Controller initialized');
    onInit();
  }
  
  /// 子类可重写的初始化方法
  void onInit() {}
  
  /// 子类可重写的销毁方法
  void onDispose() {}
  
  @override
  void dispose() {
    AppLogger.debug(tag, 'Controller disposed');
    onDispose();
    super.dispose();
  }
  
  /// 设置加载状态
  void setLoading([T? data]) {
    if (mounted) {
      state = BaseStateData.loading(data);
      AppLogger.debug(tag, 'State changed to loading');
    }
  }
  
  /// 设置成功状态
  void setSuccess(T data) {
    if (mounted) {
      state = BaseStateData.success(data);
      AppLogger.debug(tag, 'State changed to success');
    }
  }
  
  /// 设置错误状态
  void setError(String error, [T? data]) {
    if (mounted) {
      state = BaseStateData.error(error, data);
      AppLogger.error(tag, 'State changed to error: $error');
    }
  }
  
  /// 重置为初始状态
  void reset() {
    if (mounted) {
      state = BaseStateData.initial();
      AppLogger.debug(tag, 'State reset to initial');
    }
  }
  
  /// 安全执行异步操作
  /// 
  /// 自动处理加载状态和错误捕获
  Future<void> safeExecute(
    Future<T> Function() operation, {
    bool showLoading = true,
    String? errorMessage,
    void Function(T data)? onSuccess,
    void Function(String error)? onError,
  }) async {
    try {
      if (showLoading) {
        setLoading(state.data);
      }
      
      final result = await operation();
      setSuccess(result);
      
      if (onSuccess != null) {
        onSuccess(result);
      }
      
    } catch (e, stackTrace) {
      final error = errorMessage ?? e.toString();
      setError(error, state.data);
      
      AppLogger.error(tag, 'Operation failed: $error', e, stackTrace);
      
      if (onError != null) {
        onError(error);
      }
    }
  }
  
  /// 安全执行同步操作
  void safeSyncExecute(
    T Function() operation, {
    String? errorMessage,
    void Function(T data)? onSuccess,
    void Function(String error)? onError,
  }) {
    try {
      final result = operation();
      setSuccess(result);
      
      if (onSuccess != null) {
        onSuccess(result);
      }
      
    } catch (e, stackTrace) {
      final error = errorMessage ?? e.toString();
      setError(error, state.data);
      
      AppLogger.error(tag, 'Sync operation failed: $error', e, stackTrace);
      
      if (onError != null) {
        onError(error);
      }
    }
  }
  
  /// 更新数据而不改变状态
  void updateData(T data) {
    if (mounted) {
      state = state.copyWith(data: data);
      AppLogger.debug(tag, 'Data updated');
    }
  }
  
  /// 检查是否处于加载状态
  bool get isLoading => state.isLoading;
  
  /// 检查是否有错误
  bool get hasError => state.hasError;
  
  /// 检查是否有数据
  bool get hasData => state.hasData;
  
  /// 获取当前数据
  T? get data => state.data;
  
  /// 获取错误信息
  String? get error => state.error;
  
  /// 获取当前状态
  BaseState get currentState => state.state;
}

/// 列表控制器基类
/// 
/// 专门用于管理列表数据的控制器
abstract class BaseListController<T> extends BaseController<List<T>> {
  BaseListController() : super();
  
  /// 添加项目到列表
  void addItem(T item) {
    final currentList = state.data ?? <T>[];
    final newList = [...currentList, item];
    setSuccess(newList);
    AppLogger.debug(tag, 'Item added to list, total: ${newList.length}');
  }
  
  /// 添加多个项目到列表
  void addItems(List<T> items) {
    final currentList = state.data ?? <T>[];
    final newList = [...currentList, ...items];
    setSuccess(newList);
    AppLogger.debug(tag, '${items.length} items added to list, total: ${newList.length}');
  }
  
  /// 移除项目
  void removeItem(T item) {
    final currentList = state.data ?? <T>[];
    final newList = currentList.where((element) => element != item).toList();
    setSuccess(newList);
    AppLogger.debug(tag, 'Item removed from list, total: ${newList.length}');
  }
  
  /// 根据条件移除项目
  void removeWhere(bool Function(T) test) {
    final currentList = state.data ?? <T>[];
    final newList = currentList.where((element) => !test(element)).toList();
    setSuccess(newList);
    AppLogger.debug(tag, 'Items removed from list, total: ${newList.length}');
  }
  
  /// 更新项目
  void updateItem(T oldItem, T newItem) {
    final currentList = state.data ?? <T>[];
    final index = currentList.indexOf(oldItem);
    if (index != -1) {
      final newList = [...currentList];
      newList[index] = newItem;
      setSuccess(newList);
      AppLogger.debug(tag, 'Item updated in list');
    }
  }
  
  /// 根据索引更新项目
  void updateItemAt(int index, T item) {
    final currentList = state.data ?? <T>[];
    if (index >= 0 && index < currentList.length) {
      final newList = [...currentList];
      newList[index] = item;
      setSuccess(newList);
      AppLogger.debug(tag, 'Item updated at index $index');
    }
  }
  
  /// 清空列表
  void clearList() {
    setSuccess(<T>[]);
    AppLogger.debug(tag, 'List cleared');
  }
  
  /// 获取列表长度
  int get length => state.data?.length ?? 0;
  
  /// 检查列表是否为空
  bool get isEmpty => length == 0;
  
  /// 检查列表是否不为空
  bool get isNotEmpty => length > 0;
}
