// Copyright (c) 2024 VocalMind AI
import 'dart:io';
import 'dart:typed_data';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../log/app_logger.dart';

/// 文件操作工具类
class FileUtils {
  static const String _tag = 'FileUtils';
  
  /// 获取应用文档目录
  static Future<Directory> getDocumentsDirectory() async {
    return await getApplicationDocumentsDirectory();
  }
  
  /// 获取应用缓存目录
  static Future<Directory> getCacheDirectory() async {
    return await getTemporaryDirectory();
  }
  
  /// 获取应用支持目录
  static Future<Directory> getSupportDirectory() async {
    return await getApplicationSupportDirectory();
  }
  
  /// 创建目录（如果不存在）
  static Future<Directory> createDirectory(String dirPath) async {
    final directory = Directory(dirPath);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
      AppLogger.debug(_tag, 'Directory created: $dirPath');
    }
    return directory;
  }
  
  /// 获取文件大小（字节）
  static Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to get file size: $filePath', e);
      return 0;
    }
  }
  
  /// 格式化文件大小
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
  
  /// 检查文件是否存在
  static Future<bool> fileExists(String filePath) async {
    try {
      return await File(filePath).exists();
    } catch (e) {
      AppLogger.error(_tag, 'Failed to check file existence: $filePath', e);
      return false;
    }
  }
  
  /// 删除文件
  static Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        AppLogger.debug(_tag, 'File deleted: $filePath');
        return true;
      }
      return false;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to delete file: $filePath', e);
      return false;
    }
  }
  
  /// 复制文件
  static Future<bool> copyFile(String sourcePath, String destinationPath) async {
    try {
      final sourceFile = File(sourcePath);
      if (!await sourceFile.exists()) {
        AppLogger.warning(_tag, 'Source file does not exist: $sourcePath');
        return false;
      }
      
      // 确保目标目录存在
      final destinationDir = Directory(path.dirname(destinationPath));
      if (!await destinationDir.exists()) {
        await destinationDir.create(recursive: true);
      }
      
      await sourceFile.copy(destinationPath);
      AppLogger.debug(_tag, 'File copied: $sourcePath -> $destinationPath');
      return true;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to copy file: $sourcePath -> $destinationPath', e);
      return false;
    }
  }
  
  /// 移动文件
  static Future<bool> moveFile(String sourcePath, String destinationPath) async {
    try {
      final sourceFile = File(sourcePath);
      if (!await sourceFile.exists()) {
        AppLogger.warning(_tag, 'Source file does not exist: $sourcePath');
        return false;
      }
      
      // 确保目标目录存在
      final destinationDir = Directory(path.dirname(destinationPath));
      if (!await destinationDir.exists()) {
        await destinationDir.create(recursive: true);
      }
      
      await sourceFile.rename(destinationPath);
      AppLogger.debug(_tag, 'File moved: $sourcePath -> $destinationPath');
      return true;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to move file: $sourcePath -> $destinationPath', e);
      return false;
    }
  }
  
  /// 读取文件内容（文本）
  static Future<String?> readTextFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsString();
      }
      return null;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to read text file: $filePath', e);
      return null;
    }
  }
  
  /// 写入文件内容（文本）
  static Future<bool> writeTextFile(String filePath, String content) async {
    try {
      // 确保目录存在
      final directory = Directory(path.dirname(filePath));
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      
      final file = File(filePath);
      await file.writeAsString(content);
      AppLogger.debug(_tag, 'Text file written: $filePath');
      return true;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to write text file: $filePath', e);
      return false;
    }
  }
  
  /// 读取文件内容（字节）
  static Future<Uint8List?> readBinaryFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsBytes();
      }
      return null;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to read binary file: $filePath', e);
      return null;
    }
  }
  
  /// 写入文件内容（字节）
  static Future<bool> writeBinaryFile(String filePath, Uint8List bytes) async {
    try {
      // 确保目录存在
      final directory = Directory(path.dirname(filePath));
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      
      final file = File(filePath);
      await file.writeAsBytes(bytes);
      AppLogger.debug(_tag, 'Binary file written: $filePath');
      return true;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to write binary file: $filePath', e);
      return false;
    }
  }
  
  /// 获取文件扩展名
  static String getFileExtension(String filePath) {
    return path.extension(filePath).toLowerCase();
  }
  
  /// 获取文件名（不含扩展名）
  static String getFileNameWithoutExtension(String filePath) {
    return path.basenameWithoutExtension(filePath);
  }
  
  /// 获取文件名（含扩展名）
  static String getFileName(String filePath) {
    return path.basename(filePath);
  }
  
  /// 获取目录路径
  static String getDirectoryPath(String filePath) {
    return path.dirname(filePath);
  }
  
  /// 生成唯一文件名
  static String generateUniqueFileName(String baseName, String extension) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${baseName}_$timestamp$extension';
  }
  
  /// 清理目录（删除所有文件和子目录）
  static Future<bool> cleanDirectory(String dirPath) async {
    try {
      final directory = Directory(dirPath);
      if (await directory.exists()) {
        await for (final entity in directory.list()) {
          if (entity is File) {
            await entity.delete();
          } else if (entity is Directory) {
            await entity.delete(recursive: true);
          }
        }
        AppLogger.debug(_tag, 'Directory cleaned: $dirPath');
        return true;
      }
      return false;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to clean directory: $dirPath', e);
      return false;
    }
  }
  
  /// 获取目录大小
  static Future<int> getDirectorySize(String dirPath) async {
    int totalSize = 0;
    try {
      final directory = Directory(dirPath);
      if (await directory.exists()) {
        await for (final entity in directory.list(recursive: true)) {
          if (entity is File) {
            totalSize += await entity.length();
          }
        }
      }
    } catch (e) {
      AppLogger.error(_tag, 'Failed to get directory size: $dirPath', e);
    }
    return totalSize;
  }
  
  /// 列出目录中的文件
  static Future<List<String>> listFiles(String dirPath, {String? extension}) async {
    final files = <String>[];
    try {
      final directory = Directory(dirPath);
      if (await directory.exists()) {
        await for (final entity in directory.list()) {
          if (entity is File) {
            final filePath = entity.path;
            if (extension == null || getFileExtension(filePath) == extension) {
              files.add(filePath);
            }
          }
        }
      }
    } catch (e) {
      AppLogger.error(_tag, 'Failed to list files in directory: $dirPath', e);
    }
    return files;
  }
  
  /// 检查文件是否是音频文件
  static bool isAudioFile(String filePath) {
    final extension = getFileExtension(filePath);
    const audioExtensions = ['.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac'];
    return audioExtensions.contains(extension);
  }
  
  /// 检查文件是否是图片文件
  static bool isImageFile(String filePath) {
    final extension = getFileExtension(filePath);
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.contains(extension);
  }
  
  /// 检查文件是否是视频文件
  static bool isVideoFile(String filePath) {
    final extension = getFileExtension(filePath);
    const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv'];
    return videoExtensions.contains(extension);
  }
}
