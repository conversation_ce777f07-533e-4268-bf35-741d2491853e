// Copyright (c) 2024 VocalMind AI
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/router/app_router.dart';
import '../../../core/utils/date_utils.dart' as date_utils;

import '../../../shared/ui/loading_indicator.dart';
import '../controller/meeting_controller.dart';
import '../model/meeting_models.dart';

/// 会议记录列表页面
class MeetingListScreen extends ConsumerStatefulWidget {
  const MeetingListScreen({super.key});

  @override
  ConsumerState<MeetingListScreen> createState() => _MeetingListScreenState();
}

class _MeetingListScreenState extends ConsumerState<MeetingListScreen> {
  final TextEditingController _searchController = TextEditingController();
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final meetingState = ref.watch(meetingControllerProvider);
    final meetings = ref.watch(meetingListProvider);
    final hasActiveFilter = ref.watch(hasActiveMeetingFilterProvider);
    final statisticsAsync = ref.watch(meetingStatisticsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('会议记录'),
        backgroundColor: theme.colorScheme.inversePrimary,
        actions: [
          // 搜索按钮
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
          // 过滤按钮
          IconButton(
            icon: Icon(
              Icons.filter_list,
              color: hasActiveFilter ? theme.colorScheme.primary : null,
            ),
            onPressed: () => _showFilterDialog(context),
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: meetingState.isLoading,
        child: Column(
          children: [
            // 统计信息卡片
            _buildStatisticsCard(theme, statisticsAsync),
            
            // 会议记录列表
            Expanded(
              child: _buildMeetingList(theme, meetings),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go(AppRoutes.home),
        tooltip: '开始新的录音',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatisticsCard(ThemeData theme, AsyncValue<MeetingStatistics> statisticsAsync) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: statisticsAsync.when(
        data: (statistics) => Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(
              theme,
              '总记录',
              '${statistics.totalCount}',
              Icons.description,
            ),
            _buildStatItem(
              theme,
              '总时长',
              statistics.formattedTotalDuration,
              Icons.access_time,
            ),
            _buildStatItem(
              theme,
              '总字数',
              statistics.formattedTotalWordCount,
              Icons.text_fields,
            ),
          ],
        ),
        loading: () => const Center(
          child: CustomLoadingIndicator(
            size: LoadingIndicatorSize.small,
          ),
        ),
        error: (error, stack) => Text(
          '统计信息加载失败',
          style: TextStyle(
            color: theme.colorScheme.onPrimaryContainer.withOpacity(0.7),
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(ThemeData theme, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: theme.colorScheme.onPrimaryContainer,
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            color: theme.colorScheme.onPrimaryContainer,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onPrimaryContainer.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildMeetingList(ThemeData theme, List<MeetingRecord> meetings) {
    if (meetings.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description_outlined,
              size: 64,
              color: theme.colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              '暂无会议记录',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '点击右下角按钮开始录音',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.5),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(meetingControllerProvider.notifier).refreshMeetings();
      },
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: meetings.length,
        itemBuilder: (context, index) {
          final meeting = meetings[index];
          return _buildMeetingCard(theme, meeting);
        },
      ),
    );
  }

  Widget _buildMeetingCard(ThemeData theme, MeetingRecord meeting) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => context.go(AppRoutes.buildMeetingDetailRoute(meeting.id)),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题和状态
              Row(
                children: [
                  Expanded(
                    child: Text(
                      meeting.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildStatusChip(theme, meeting.status),
                ],
              ),
              const SizedBox(height: 8),
              
              // 内容预览
              Text(
                meeting.displayContent,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              
              // 元信息
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: theme.colorScheme.outline,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    date_utils.DateUtils.formatRelativeTime(meeting.createdAt),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  if (meeting.duration > 0) ...[
                    Icon(
                      Icons.timer,
                      size: 16,
                      color: theme.colorScheme.outline,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      meeting.formattedDuration,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.outline,
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                  
                  Icon(
                    Icons.text_fields,
                    size: 16,
                    color: theme.colorScheme.outline,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${meeting.wordCount}字',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // 更多操作按钮
                  IconButton(
                    icon: const Icon(Icons.more_vert),
                    onPressed: () => _showMeetingOptions(context, meeting),
                    iconSize: 20,
                  ),
                ],
              ),
              
              // 标签
              if (meeting.tags.isNotEmpty) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: meeting.tags.take(3).map((tag) {
                    return Chip(
                      label: Text(
                        tag,
                        style: theme.textTheme.bodySmall,
                      ),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(ThemeData theme, MeetingStatus status) {
    Color backgroundColor;
    Color textColor;
    
    switch (status) {
      case MeetingStatus.draft:
        backgroundColor = theme.colorScheme.surfaceVariant;
        textColor = theme.colorScheme.onSurfaceVariant;
        break;
      case MeetingStatus.processing:
        backgroundColor = theme.colorScheme.primaryContainer;
        textColor = theme.colorScheme.onPrimaryContainer;
        break;
      case MeetingStatus.completed:
        backgroundColor = theme.colorScheme.secondaryContainer;
        textColor = theme.colorScheme.onSecondaryContainer;
        break;
      case MeetingStatus.archived:
        backgroundColor = theme.colorScheme.outline.withOpacity(0.1);
        textColor = theme.colorScheme.outline;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.displayName,
        style: theme.textTheme.bodySmall?.copyWith(
          color: textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('搜索会议记录'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: '输入关键词搜索标题或内容',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final keyword = _searchController.text.trim();
              if (keyword.isNotEmpty) {
                ref.read(meetingControllerProvider.notifier).searchMeetings(keyword);
              }
              Navigator.of(context).pop();
            },
            child: const Text('搜索'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    // TODO: 实现过滤对话框
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('过滤功能开发中...'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showMeetingOptions(BuildContext context, MeetingRecord meeting) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.edit),
            title: const Text('编辑'),
            onTap: () {
              Navigator.of(context).pop();
              // TODO: 实现编辑功能
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('分享'),
            onTap: () {
              Navigator.of(context).pop();
              // TODO: 实现分享功能
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete),
            title: const Text('删除'),
            onTap: () {
              Navigator.of(context).pop();
              _confirmDelete(context, meeting);
            },
          ),
        ],
      ),
    );
  }

  void _confirmDelete(BuildContext context, MeetingRecord meeting) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除会议记录"${meeting.title}"吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(meetingControllerProvider.notifier).deleteMeeting(meeting.id);
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
