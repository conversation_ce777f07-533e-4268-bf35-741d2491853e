// Copyright (c) 2024 VocalMind AI
import 'package:flutter_riverpod/flutter_riverpod.dart';


import '../../../core/log/app_logger.dart';
import '../../../shared/service/storage_service.dart';
import '../../meeting/model/meeting_models.dart';
import '../../meeting/service/meeting_database_service.dart';
import '../model/chat_models.dart';
import '../service/llm_service.dart';

/// AI聊天状态
class AiChatState {
  final ChatSession? currentSession;
  final List<ChatMessage> messages;
  final bool isLoading;
  final bool isStreaming;
  final String? error;
  final LLMConfig llmConfig;

  const AiChatState({
    this.currentSession,
    this.messages = const [],
    this.isLoading = false,
    this.isStreaming = false,
    this.error,
    required this.llmConfig,
  });

  AiChatState copyWith({
    ChatSession? currentSession,
    List<ChatMessage>? messages,
    bool? isLoading,
    bool? isStreaming,
    String? error,
    LLMConfig? llmConfig,
  }) {
    return AiChatState(
      currentSession: currentSession ?? this.currentSession,
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      isStreaming: isStreaming ?? this.isStreaming,
      error: error,
      llmConfig: llmConfig ?? this.llmConfig,
    );
  }
}

/// AI聊天控制器
class AiChatController extends StateNotifier<AiChatState> {
  final LLMService _llmService;
  final StorageService _storageService;
  final MeetingDatabaseService _meetingService;
  
  MeetingRecord? _meetingContext;

  AiChatController({
    LLMService? llmService,
    StorageService? storageService,
    MeetingDatabaseService? meetingService,
  }) : _llmService = llmService ?? LLMService(),
       _storageService = storageService ?? StorageService.instance,
       _meetingService = meetingService ?? MeetingDatabaseService.instance,
       super(AiChatState(llmConfig: LLMConfig.defaultConfig()));

  String get tag => 'AiChatController';

  void initialize() {
    _loadLLMConfig();
  }

  /// 初始化聊天会话（基于会议记录）
  Future<void> initializeChatWithMeeting(String meetingId) async {
    try {
      // 获取会议记录
      _meetingContext = await _meetingService.getMeetingById(meetingId);

      if (_meetingContext == null) {
        throw Exception('Meeting record not found');
      }

      // 创建新的聊天会话
      final session = ChatSession.create(
        meetingRecordId: meetingId,
        title: '关于"${_meetingContext!.title}"的对话',
        metadata: {
          'meeting_title': _meetingContext!.title,
          'meeting_created_at': _meetingContext!.createdAt.toIso8601String(),
        },
      );

      state = AiChatState(
        currentSession: session,
        messages: [],
        llmConfig: state.llmConfig,
      );

      AppLogger.info(tag, 'Chat session initialized with meeting: ${_meetingContext?.title}');
    } catch (e) {
      AppLogger.error(tag, 'Failed to initialize chat session: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// 发送消息
  Future<void> sendMessage(String message) async {
    if (message.trim().isEmpty || state.isStreaming) return;

    final userMessage = ChatMessage.user(
      content: message.trim(),
      meetingRecordId: state.currentSession?.meetingRecordId,
    );

    // 立即添加用户消息
    final updatedMessages = List<ChatMessage>.from(state.messages)..add(userMessage);
    state = state.copyWith(
      messages: updatedMessages,
      isStreaming: true,
      error: null,
    );

    // 创建AI消息占位符
    final aiMessage = ChatMessage.ai(
      content: '',
      meetingRecordId: state.currentSession?.meetingRecordId,
      status: ChatMessageStatus.sending,
    );

    final messagesWithAi = List<ChatMessage>.from(updatedMessages)..add(aiMessage);
    state = state.copyWith(messages: messagesWithAi);

    try {
      // 发送流式请求
      final aiMessageIndex = messagesWithAi.length - 1;
      String accumulatedContent = '';

      await for (final chunk in _llmService.sendChatMessageStream(
        message: message.trim(),
        config: state.llmConfig,
        meetingContext: _meetingContext?.originalContent,
        chatHistory: state.messages.where((m) => m.isFromUser || m.status == ChatMessageStatus.sent).toList(),
      )) {
        accumulatedContent += chunk;
        
        // 更新AI消息内容
        final updatedAiMessage = aiMessage.copyWith(
          content: accumulatedContent,
          status: ChatMessageStatus.sending,
        );

        final updatedMessagesList = List<ChatMessage>.from(state.messages);
        updatedMessagesList[aiMessageIndex] = updatedAiMessage;

        state = state.copyWith(messages: updatedMessagesList);
      }

      // 标记AI消息为已发送
      final finalAiMessage = aiMessage.copyWith(
        content: accumulatedContent,
        status: ChatMessageStatus.sent,
      );

      final finalMessagesList = List<ChatMessage>.from(state.messages);
      finalMessagesList[aiMessageIndex] = finalAiMessage;

      state = state.copyWith(
        messages: finalMessagesList,
        isStreaming: false,
      );

      // 保存消息到本地存储
      await _saveMessagesToStorage();

      AppLogger.info(tag, 'Message sent successfully');

    } catch (e) {
      AppLogger.error(tag, 'Failed to send message: $e');
      
      // 标记AI消息为错误状态
      final errorAiMessage = aiMessage.copyWith(
        content: '抱歉，发送消息时出现错误，请稍后重试。',
        status: ChatMessageStatus.error,
      );

      final errorMessagesList = List<ChatMessage>.from(state.messages);
      if (errorMessagesList.isNotEmpty) {
        errorMessagesList[errorMessagesList.length - 1] = errorAiMessage;
      }

      state = state.copyWith(
        messages: errorMessagesList,
        isStreaming: false,
        error: e.toString(),
      );
    }
  }

  /// 重新发送消息
  Future<void> resendMessage(ChatMessage message) async {
    if (!message.isFromUser) return;
    
    // 移除错误的AI回复
    final messages = List<ChatMessage>.from(state.messages);
    final messageIndex = messages.indexOf(message);
    if (messageIndex >= 0 && messageIndex < messages.length - 1) {
      final nextMessage = messages[messageIndex + 1];
      if (!nextMessage.isFromUser && nextMessage.status == ChatMessageStatus.error) {
        messages.removeAt(messageIndex + 1);
      }
    }

    state = state.copyWith(messages: messages);
    
    // 重新发送
    await sendMessage(message.content);
  }

  /// 清空聊天记录
  void clearChat() {
    state = state.copyWith(
      messages: [],
      error: null,
    );
    _clearStoredMessages();
    AppLogger.info(tag, 'Chat cleared');
  }

  /// 生成会议摘要
  Future<void> generateMeetingSummary() async {
    if (_meetingContext == null) return;

    try {
      final summary = await _llmService.generateMeetingSummary(
        meetingContent: _meetingContext!.originalContent,
        config: state.llmConfig,
      );

      // 添加摘要消息
      final summaryMessage = ChatMessage.ai(
        content: '**会议摘要：**\n\n$summary',
        meetingRecordId: _meetingContext!.id,
      );

      final updatedMessages = List<ChatMessage>.from(state.messages)..add(summaryMessage);

      state = state.copyWith(messages: updatedMessages);

      AppLogger.info(tag, 'Meeting summary generated');
      await _saveMessagesToStorage();
    } catch (e) {
      AppLogger.error(tag, 'Failed to generate meeting summary: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// 加载LLM配置
  Future<void> _loadLLMConfig() async {
    try {
      final configMap = _storageService.getJson('llm_config');
      if (configMap != null) {
        final config = LLMConfig.fromJson(configMap);
        state = state.copyWith(llmConfig: config);
      }
    } catch (e) {
      AppLogger.warning(tag, 'Failed to load LLM config, using default: $e');
    }
  }

  /// 保存消息到本地存储
  Future<void> _saveMessagesToStorage() async {
    if (state.currentSession == null) return;

    try {
      final messagesJson = state.messages.map((m) => m.toJson()).toList();
      _storageService.setJson('chat_messages_${state.currentSession!.id}', {'messages': messagesJson});
    } catch (e) {
      AppLogger.warning(tag, 'Failed to save messages to storage: $e');
    }
  }

  /// 清空存储的消息
  void _clearStoredMessages() {
    if (state.currentSession == null) return;

    try {
      _storageService.remove('chat_messages_${state.currentSession!.id}');
    } catch (e) {
      AppLogger.warning(tag, 'Failed to clear stored messages: $e');
    }
  }
}

/// AI聊天控制器提供者
final aiChatControllerProvider = StateNotifierProvider<AiChatController, AiChatState>((ref) {
  return AiChatController();
});
