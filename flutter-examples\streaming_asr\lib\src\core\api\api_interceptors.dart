// Copyright (c) 2024 VocalMind AI
import 'package:dio/dio.dart';
import '../log/app_logger.dart';

/// 请求拦截器
/// 
/// 在请求发送前进行处理：
/// - 添加通用请求头
/// - 添加认证信息
/// - 请求日志记录
class RequestInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 添加请求时间戳
    options.headers['X-Request-Time'] = DateTime.now().millisecondsSinceEpoch;
    
    // 添加用户代理
    options.headers['User-Agent'] = 'VocalMind-Flutter/1.0';
    
    // 记录请求日志
    AppLogger.info('HTTP', 'Request: ${options.method} ${options.uri}');
    
    super.onRequest(options, handler);
  }
}

/// 响应拦截器
/// 
/// 在响应返回后进行处理：
/// - 响应时间计算
/// - 通用响应处理
/// - 响应日志记录
class ResponseInterceptor extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 计算响应时间
    final requestTime = response.requestOptions.headers['X-Request-Time'] as int?;
    if (requestTime != null) {
      final responseTime = DateTime.now().millisecondsSinceEpoch - requestTime;
      AppLogger.info('HTTP', 
          'Response: ${response.statusCode} ${response.requestOptions.uri} (${responseTime}ms)');
    }
    
    super.onResponse(response, handler);
  }
}

/// 错误拦截器
/// 
/// 处理请求错误：
/// - 网络错误处理
/// - HTTP状态码错误处理
/// - 认证错误自动处理
/// - 重试机制
class ErrorInterceptor extends Interceptor {
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 1);
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    AppLogger.error('HTTP', 'Request error: ${err.type} ${err.message}');
    
    // 处理特定错误类型
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        // 超时错误，尝试重试
        if (await _shouldRetry(err)) {
          try {
            final response = await _retry(err.requestOptions);
            handler.resolve(response);
            return;
          } catch (e) {
            AppLogger.error('HTTP', 'Retry failed: $e');
          }
        }
        break;
        
      case DioExceptionType.badResponse:
        // HTTP错误状态码处理
        await _handleHttpError(err);
        break;
        
      case DioExceptionType.connectionError:
        // 连接错误
        AppLogger.error('HTTP', 'Connection error: ${err.message}');
        break;
        
      default:
        break;
    }
    
    super.onError(err, handler);
  }
  
  /// 判断是否应该重试
  Future<bool> _shouldRetry(DioException err) async {
    // 获取重试次数
    final retryCount = err.requestOptions.extra['retry_count'] as int? ?? 0;
    
    // 超过最大重试次数
    if (retryCount >= maxRetries) {
      return false;
    }
    
    // 只对特定错误类型重试
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.connectionError:
        return true;
      default:
        return false;
    }
  }
  
  /// 执行重试
  Future<Response> _retry(RequestOptions requestOptions) async {
    // 增加重试次数
    final retryCount = requestOptions.extra['retry_count'] as int? ?? 0;
    requestOptions.extra['retry_count'] = retryCount + 1;
    
    // 等待一段时间后重试
    await Future.delayed(retryDelay * (retryCount + 1));
    
    AppLogger.info('HTTP', 'Retrying request (${retryCount + 1}/$maxRetries): ${requestOptions.uri}');
    
    // 创建新的Dio实例进行重试
    final dio = Dio();
    return await dio.fetch(requestOptions);
  }
  
  /// 处理HTTP错误
  Future<void> _handleHttpError(DioException err) async {
    final statusCode = err.response?.statusCode;
    
    switch (statusCode) {
      case 401:
        // 认证失败，清除本地认证信息
        AppLogger.warning('HTTP', 'Authentication failed, clearing local auth');
        await _clearAuthToken();
        break;
        
      case 403:
        // 权限不足
        AppLogger.warning('HTTP', 'Access forbidden');
        break;
        
      case 404:
        // 资源未找到
        AppLogger.warning('HTTP', 'Resource not found: ${err.requestOptions.uri}');
        break;
        
      case 429:
        // 请求过于频繁
        AppLogger.warning('HTTP', 'Rate limit exceeded');
        break;
        
      case 500:
      case 502:
      case 503:
      case 504:
        // 服务器错误
        AppLogger.error('HTTP', 'Server error: $statusCode');
        break;
        
      default:
        AppLogger.error('HTTP', 'HTTP error: $statusCode ${err.response?.statusMessage}');
    }
  }
  
  /// 清除认证Token
  Future<void> _clearAuthToken() async {
    // TODO: 实现清除本地存储的认证信息
    // 这里应该调用认证服务来清除token
  }
}

/// 缓存拦截器
/// 
/// 实现HTTP响应缓存：
/// - GET请求缓存
/// - 缓存过期处理
/// - 离线模式支持
class CacheInterceptor extends Interceptor {
  final Map<String, CacheItem> _cache = {};
  final Duration defaultCacheDuration = const Duration(minutes: 5);
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 只缓存GET请求
    if (options.method.toUpperCase() != 'GET') {
      super.onRequest(options, handler);
      return;
    }
    
    final cacheKey = _generateCacheKey(options);
    final cacheItem = _cache[cacheKey];
    
    // 检查缓存是否有效
    if (cacheItem != null && !cacheItem.isExpired) {
      AppLogger.debug('HTTP', 'Cache hit: ${options.uri}');
      handler.resolve(cacheItem.response);
      return;
    }
    
    super.onRequest(options, handler);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 只缓存成功的GET请求
    if (response.requestOptions.method.toUpperCase() == 'GET' &&
        response.statusCode != null &&
        response.statusCode! >= 200 &&
        response.statusCode! < 300) {
      
      final cacheKey = _generateCacheKey(response.requestOptions);
      final cacheDuration = _getCacheDuration(response.requestOptions);
      
      _cache[cacheKey] = CacheItem(
        response: response,
        expireTime: DateTime.now().add(cacheDuration),
      );
      
      AppLogger.debug('HTTP', 'Response cached: ${response.requestOptions.uri}');
    }
    
    super.onResponse(response, handler);
  }
  
  /// 生成缓存键
  String _generateCacheKey(RequestOptions options) {
    final uri = options.uri.toString();
    final headers = options.headers.toString();
    return '$uri#$headers'.hashCode.toString();
  }
  
  /// 获取缓存时长
  Duration _getCacheDuration(RequestOptions options) {
    // 可以从请求选项中获取自定义缓存时长
    final customDuration = options.extra['cache_duration'] as Duration?;
    return customDuration ?? defaultCacheDuration;
  }
  
  /// 清除缓存
  void clearCache() {
    _cache.clear();
    AppLogger.info('HTTP', 'Cache cleared');
  }
  
  /// 清除过期缓存
  void clearExpiredCache() {
    final now = DateTime.now();
    _cache.removeWhere((key, item) => item.expireTime.isBefore(now));
    AppLogger.debug('HTTP', 'Expired cache cleared');
  }
}

/// 缓存项
class CacheItem {
  final Response response;
  final DateTime expireTime;
  
  CacheItem({
    required this.response,
    required this.expireTime,
  });
  
  bool get isExpired => DateTime.now().isAfter(expireTime);
}
