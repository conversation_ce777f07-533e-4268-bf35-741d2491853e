// Copyright (c) 2024 VocalMind AI
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../log/app_logger.dart';
import 'api_response.dart';
import 'api_interceptors.dart';

/// HTTP API客户端封装
/// 
/// 提供统一的网络请求接口，包含：
/// - 请求/响应拦截器
/// - 错误处理
/// - 日志记录
/// - 超时配置
class ApiClient {
  static ApiClient? _instance;
  late final Dio _dio;
  
  /// 单例模式获取实例
  static ApiClient get instance {
    _instance ??= ApiClient._internal();
    return _instance!;
  }
  
  ApiClient._internal() {
    _dio = Dio();
    _setupInterceptors();
    _setupDefaultOptions();
  }
  
  /// 配置默认选项
  void _setupDefaultOptions() {
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );
  }
  
  /// 设置拦截器
  void _setupInterceptors() {
    // 请求拦截器
    _dio.interceptors.add(RequestInterceptor());
    
    // 响应拦截器
    _dio.interceptors.add(ResponseInterceptor());
    
    // 错误拦截器
    _dio.interceptors.add(ErrorInterceptor());
    
    // 日志拦截器（仅在调试模式下）
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
        error: true,
        logPrint: (obj) => AppLogger.debug('HTTP', obj.toString()),
      ));
    }
  }
  
  /// 设置基础URL
  void setBaseUrl(String baseUrl) {
    _dio.options.baseUrl = baseUrl;
  }
  
  /// 设置认证Token
  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }
  
  /// 清除认证Token
  void clearAuthToken() {
    _dio.options.headers.remove('Authorization');
  }
  
  /// GET请求
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      AppLogger.error('ApiClient', 'Unexpected error in GET request: $e');
      return ApiResponse.error('Unexpected error occurred');
    }
  }
  
  /// POST请求
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      AppLogger.error('ApiClient', 'Unexpected error in POST request: $e');
      return ApiResponse.error('Unexpected error occurred');
    }
  }
  
  /// PUT请求
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      AppLogger.error('ApiClient', 'Unexpected error in PUT request: $e');
      return ApiResponse.error('Unexpected error occurred');
    }
  }
  
  /// DELETE请求
  Future<ApiResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      AppLogger.error('ApiClient', 'Unexpected error in DELETE request: $e');
      return ApiResponse.error('Unexpected error occurred');
    }
  }
  
  /// 文件上传
  Future<ApiResponse<T>> uploadFile<T>(
    String path,
    String filePath, {
    String? fileName,
    Map<String, dynamic>? data,
    ProgressCallback? onSendProgress,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(filePath, filename: fileName),
        ...?data,
      });
      
      final response = await _dio.post(
        path,
        data: formData,
        onSendProgress: onSendProgress,
      );
      
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      AppLogger.error('ApiClient', 'Unexpected error in file upload: $e');
      return ApiResponse.error('File upload failed');
    }
  }
  
  /// 处理响应
  ApiResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? fromJson,
  ) {
    if (response.statusCode != null && 
        response.statusCode! >= 200 && 
        response.statusCode! < 300) {
      
      if (fromJson != null && response.data != null) {
        try {
          final data = fromJson(response.data);
          return ApiResponse.success(data);
        } catch (e) {
          AppLogger.error('ApiClient', 'JSON parsing error: $e');
          return ApiResponse.error('Data parsing failed');
        }
      } else {
        return ApiResponse.success(response.data as T);
      }
    } else {
      return ApiResponse.error(
        'HTTP ${response.statusCode}: ${response.statusMessage}',
        statusCode: response.statusCode,
      );
    }
  }
  
  /// 处理错误
  ApiResponse<T> _handleError<T>(DioException error) {
    String message;
    int? statusCode;
    
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        message = 'Connection timeout';
        break;
      case DioExceptionType.sendTimeout:
        message = 'Send timeout';
        break;
      case DioExceptionType.receiveTimeout:
        message = 'Receive timeout';
        break;
      case DioExceptionType.badResponse:
        statusCode = error.response?.statusCode;
        message = _extractErrorMessage(error.response?.data) ?? 
                 'HTTP ${statusCode}: ${error.response?.statusMessage}';
        break;
      case DioExceptionType.cancel:
        message = 'Request cancelled';
        break;
      case DioExceptionType.connectionError:
        message = 'Connection error';
        break;
      default:
        message = error.message ?? 'Unknown error occurred';
    }
    
    AppLogger.error('ApiClient', 'Request failed: $message');
    return ApiResponse.error(message, statusCode: statusCode);
  }
  
  /// 从响应数据中提取错误消息
  String? _extractErrorMessage(dynamic data) {
    if (data is Map<String, dynamic>) {
      return data['message'] ?? data['error'] ?? data['msg'];
    }
    return null;
  }
  
  /// 取消所有请求
  void cancelAllRequests() {
    _dio.close(force: true);
  }
}
