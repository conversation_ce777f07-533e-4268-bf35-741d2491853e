# VocalMind AI Android to Flutter Migration Plan

## 项目概述

本文档详细描述了将VocalMind AI Android语音助手应用迁移到Flutter的完整计划。Android应用是一个功能完整的语音识别和AI交互应用，包含用户认证、实时语音转录、会议记录管理、AI聊天、待办事项管理等核心功能。

## 目标架构

### Flutter项目结构
遵循项目现有的三层架构模式：
```
lib/src/
├── core/           # 核心框架层
│   ├── api/        # API客户端封装
│   ├── base/       # 抽象基类
│   ├── extensions/ # Dart语言扩展
│   ├── log/        # 日志服务
│   ├── router/     # 导航路由
│   └── utils/      # 通用工具函数
├── shared/         # 跨功能共享业务层
│   ├── controller/ # 共享控制器
│   ├── model/      # 共享数据模型
│   ├── service/    # 共享服务
│   └── ui/         # 共享UI组件
└── features/       # 独立功能层
    ├── auth/       # 用户认证模块
    ├── asr/        # 语音识别模块
    ├── meeting/    # 会议记录管理
    ├── ai_chat/    # AI交互模块
    ├── todo/       # 待办事项模块
    └── settings/   # 设置模块
```

## 核心功能模块分析

### 1. 用户认证模块 (auth)
**Android组件**: WelcomeActivity, LoginActivity, RegisterActivity, ForgotPasswordActivity
**Flutter实现**:
- `features/auth/ui/` - 认证相关页面
- `features/auth/controller/` - 认证逻辑控制器
- `features/auth/model/` - 用户模型和认证状态
- `features/auth/service/` - 认证API服务

**核心功能**:
- 欢迎页面和功能介绍
- 用户注册/登录
- 忘记密码
- 认证状态管理
- 本地存储用户信息

### 2. 实时语音识别模块 (asr)
**Android组件**: SingleModelActivity, AudioRecordingService
**Flutter实现**:
- `features/asr/ui/` - 语音识别界面
- `features/asr/controller/` - ASR控制逻辑
- `features/asr/model/` - 识别结果模型
- `features/asr/service/` - sherpa-onnx集成服务

**核心功能**:
- 实时音频录制
- sherpa-onnx语音识别引擎集成
- 实时转录结果显示
- 录音状态管理
- 音频文件导入转录
- 自动保存会议记录

### 3. 会议记录管理模块 (meeting)
**Android组件**: MeetingRecordsActivity, MeetingDetailActivity
**Flutter实现**:
- `features/meeting/ui/` - 会议记录界面
- `features/meeting/controller/` - 记录管理控制器
- `features/meeting/model/` - 会议记录模型
- `features/meeting/service/` - 本地存储服务

**核心功能**:
- 会议记录列表展示
- 会议详情查看
- 记录搜索和筛选
- 记录导出功能
- 标签和分类管理

### 4. AI交互模块 (ai_chat)
**Android组件**: AiChatActivity
**Flutter实现**:
- `features/ai_chat/ui/` - AI聊天界面
- `features/ai_chat/controller/` - 聊天控制器
- `features/ai_chat/model/` - 聊天消息模型
- `features/ai_chat/service/` - LLM API服务

**核心功能**:
- 基于会议内容的AI问答
- 聊天历史记录
- 多种AI模型支持
- 内容总结和分析

### 5. 待办事项模块 (todo)
**Android组件**: TodoActivity, TodoEditActivity, TodoReminderReceiver
**Flutter实现**:
- `features/todo/ui/` - 待办事项界面
- `features/todo/controller/` - 待办管理控制器
- `features/todo/model/` - 待办事项模型
- `features/todo/service/` - 提醒服务

**核心功能**:
- 待办事项创建和编辑
- 基于会议内容自动生成待办
- 定时提醒功能
- 待办状态管理

### 6. 设置模块 (settings)
**Android组件**: SettingsActivity, ServerSettingsActivity
**Flutter实现**:
- `features/settings/ui/` - 设置界面
- `features/settings/controller/` - 设置控制器
- `features/settings/model/` - 设置配置模型
- `features/settings/service/` - 配置存储服务

**核心功能**:
- 应用基础设置
- 服务器配置
- LLM模型配置
- 隐私设置

## 技术栈选择

### 状态管理
- **Riverpod**: 已集成，用于响应式状态管理
- **单向数据流**: Controller → Model → UI

### 导航
- **go_router**: 已集成，用于声明式路由管理

### 本地存储
- **shared_preferences**: 用户设置和简单配置
- **sqflite**: 会议记录和待办事项数据库
- **path_provider**: 文件路径管理

### 网络请求
- **dio**: HTTP客户端
- **retrofit**: API接口生成

### 音频处理
- **sherpa_onnx**: 已集成，语音识别引擎
- **record**: 已集成，音频录制
- **audioplayers**: 音频播放

### UI组件
- **Material Design 3**: 主要UI风格
- **flutter_animate**: 动画效果
- **flutter_staggered_grid_view**: 网格布局

## 迁移策略

### 阶段1: 基础架构搭建 (1-2周)
1. 完善core层基础设施
2. 建立共享服务和模型
3. 配置路由系统
4. 建立状态管理模式

### 阶段2: 核心功能迁移 (3-4周)
1. 用户认证模块
2. 语音识别核心功能
3. 会议记录基础管理

### 阶段3: 高级功能实现 (2-3周)
1. AI交互功能
2. 待办事项管理
3. 高级设置功能

### 阶段4: 优化和完善 (1-2周)
1. 性能优化
2. UI/UX改进
3. 测试和调试
4. 文档完善

## 关键技术挑战

### 1. 后台音频录制
- **Android**: AudioRecordingService (前台服务)
- **Flutter**: 需要使用platform channels或后台插件
- **解决方案**: 使用flutter_background_service + record插件

### 2. 悬浮窗功能
- **Android**: FloatingWindowService
- **Flutter**: 需要原生平台实现
- **解决方案**: 使用system_alert_window插件或自定义platform channel

### 3. 系统通知和提醒
- **Android**: NotificationManager + AlarmManager
- **Flutter**: flutter_local_notifications + android_alarm_manager_plus

### 4. 文件管理和导入
- **Android**: Intent处理和文件访问
- **Flutter**: file_picker + path_provider

## 数据迁移计划

### 用户数据
- 认证信息: SharedPreferences迁移
- 用户设置: JSON格式存储

### 会议记录
- SQLite数据库设计
- 音频文件路径管理
- 搜索索引建立

### 待办事项
- 本地数据库存储
- 提醒时间管理
- 状态同步机制

## 质量保证

### 测试策略
- 单元测试: 业务逻辑测试
- Widget测试: UI组件测试
- 集成测试: 端到端功能测试

### 性能监控
- 内存使用监控
- 音频处理性能
- UI响应性能

### 兼容性测试
- Android版本兼容性
- 不同设备适配
- 权限处理测试

## 风险评估

### 高风险项
1. sherpa-onnx Flutter集成的稳定性
2. 后台音频录制的权限和稳定性
3. 大量音频数据的内存管理

### 中风险项
1. AI API集成的网络稳定性
2. 本地数据库性能
3. UI适配不同屏幕尺寸

### 低风险项
1. 基础UI组件实现
2. 用户认证流程
3. 设置页面功能

## 成功标准

### 功能完整性
- [ ] 所有Android功能在Flutter中实现
- [ ] 用户体验保持一致或改进
- [ ] 性能指标达到或超过Android版本

### 代码质量
- [ ] 遵循Flutter最佳实践
- [ ] 代码覆盖率 > 80%
- [ ] 无严重性能问题

### 用户体验
- [ ] 启动时间 < 3秒
- [ ] 音频识别延迟 < 500ms
- [ ] UI响应时间 < 100ms

## 后续维护计划

### 版本发布
- 每2周一个迭代版本
- 每月一个稳定版本
- 重大功能独立发版

### 持续改进
- 用户反馈收集
- 性能监控和优化
- 新功能开发规划
