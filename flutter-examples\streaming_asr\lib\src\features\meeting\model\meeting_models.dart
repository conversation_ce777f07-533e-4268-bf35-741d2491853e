// Copyright (c) 2024 VocalMind AI
import 'package:uuid/uuid.dart';

/// 会议记录模型
class MeetingRecord {
  final String id;
  final String title;
  final String originalContent;
  final String? optimizedContent;
  final String? summary;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int duration; // 录音时长（秒）
  final int wordCount;
  final int speakerCount;
  final String? audioFilePath;
  final List<String> tags;
  final MeetingStatus status;
  final Map<String, dynamic>? metadata;

  const MeetingRecord({
    required this.id,
    required this.title,
    required this.originalContent,
    this.optimizedContent,
    this.summary,
    required this.createdAt,
    required this.updatedAt,
    this.duration = 0,
    this.wordCount = 0,
    this.speakerCount = 1,
    this.audioFilePath,
    this.tags = const [],
    this.status = MeetingStatus.draft,
    this.metadata,
  });

  /// 创建新的会议记录
  factory MeetingRecord.create({
    required String title,
    required String originalContent,
    int? duration,
    int? wordCount,
    int? speakerCount,
    String? audioFilePath,
    List<String>? tags,
  }) {
    final now = DateTime.now();
    return MeetingRecord(
      id: const Uuid().v4(),
      title: title,
      originalContent: originalContent,
      createdAt: now,
      updatedAt: now,
      duration: duration ?? 0,
      wordCount: wordCount ?? originalContent.length,
      speakerCount: speakerCount ?? 1,
      audioFilePath: audioFilePath,
      tags: tags ?? [],
    );
  }

  /// 从JSON创建会议记录
  factory MeetingRecord.fromJson(Map<String, dynamic> json) {
    return MeetingRecord(
      id: json['id'] as String,
      title: json['title'] as String,
      originalContent: json['original_content'] as String,
      optimizedContent: json['optimized_content'] as String?,
      summary: json['summary'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      duration: json['duration'] as int? ?? 0,
      wordCount: json['word_count'] as int? ?? 0,
      speakerCount: json['speaker_count'] as int? ?? 1,
      audioFilePath: json['audio_file_path'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      status: MeetingStatus.fromString(json['status'] as String? ?? 'draft'),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'original_content': originalContent,
      'optimized_content': optimizedContent,
      'summary': summary,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'duration': duration,
      'word_count': wordCount,
      'speaker_count': speakerCount,
      'audio_file_path': audioFilePath,
      'tags': tags,
      'status': status.value,
      'metadata': metadata,
    };
  }

  /// 复制并修改部分属性
  MeetingRecord copyWith({
    String? id,
    String? title,
    String? originalContent,
    String? optimizedContent,
    String? summary,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? duration,
    int? wordCount,
    int? speakerCount,
    String? audioFilePath,
    List<String>? tags,
    MeetingStatus? status,
    Map<String, dynamic>? metadata,
  }) {
    return MeetingRecord(
      id: id ?? this.id,
      title: title ?? this.title,
      originalContent: originalContent ?? this.originalContent,
      optimizedContent: optimizedContent ?? this.optimizedContent,
      summary: summary ?? this.summary,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      duration: duration ?? this.duration,
      wordCount: wordCount ?? this.wordCount,
      speakerCount: speakerCount ?? this.speakerCount,
      audioFilePath: audioFilePath ?? this.audioFilePath,
      tags: tags ?? this.tags,
      status: status ?? this.status,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 获取显示用的内容
  String get displayContent => optimizedContent ?? originalContent;

  /// 检查是否有音频文件
  bool get hasAudio => audioFilePath != null && audioFilePath!.isNotEmpty;

  /// 检查是否有优化内容
  bool get hasOptimizedContent => optimizedContent != null && optimizedContent!.isNotEmpty;

  /// 检查是否有摘要
  bool get hasSummary => summary != null && summary!.isNotEmpty;

  /// 格式化时长
  String get formattedDuration {
    if (duration <= 0) return '00:00';
    
    final hours = duration ~/ 3600;
    final minutes = (duration % 3600) ~/ 60;
    final seconds = duration % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
             '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    }
  }

  @override
  String toString() {
    return 'MeetingRecord(id: $id, title: $title, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MeetingRecord && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 会议状态枚举
enum MeetingStatus {
  draft('draft'),
  processing('processing'),
  completed('completed'),
  archived('archived');

  const MeetingStatus(this.value);

  final String value;

  static MeetingStatus fromString(String value) {
    return MeetingStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => MeetingStatus.draft,
    );
  }

  String get displayName {
    switch (this) {
      case MeetingStatus.draft:
        return '草稿';
      case MeetingStatus.processing:
        return '处理中';
      case MeetingStatus.completed:
        return '已完成';
      case MeetingStatus.archived:
        return '已归档';
    }
  }

  @override
  String toString() => value;
}

/// 会议搜索过滤器
class MeetingFilter {
  final String? keyword;
  final List<String>? tags;
  final MeetingStatus? status;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? minDuration;
  final int? maxDuration;
  final MeetingSortBy sortBy;
  final bool ascending;

  const MeetingFilter({
    this.keyword,
    this.tags,
    this.status,
    this.startDate,
    this.endDate,
    this.minDuration,
    this.maxDuration,
    this.sortBy = MeetingSortBy.createdAt,
    this.ascending = false,
  });

  /// 复制并修改部分属性
  MeetingFilter copyWith({
    String? keyword,
    List<String>? tags,
    MeetingStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? minDuration,
    int? maxDuration,
    MeetingSortBy? sortBy,
    bool? ascending,
  }) {
    return MeetingFilter(
      keyword: keyword ?? this.keyword,
      tags: tags ?? this.tags,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      minDuration: minDuration ?? this.minDuration,
      maxDuration: maxDuration ?? this.maxDuration,
      sortBy: sortBy ?? this.sortBy,
      ascending: ascending ?? this.ascending,
    );
  }

  /// 检查是否有活跃的过滤条件
  bool get hasActiveFilters {
    return keyword != null ||
           tags != null ||
           status != null ||
           startDate != null ||
           endDate != null ||
           minDuration != null ||
           maxDuration != null;
  }

  @override
  String toString() {
    return 'MeetingFilter(keyword: $keyword, status: $status, sortBy: $sortBy)';
  }
}

/// 会议排序方式
enum MeetingSortBy {
  createdAt('created_at'),
  updatedAt('updated_at'),
  title('title'),
  duration('duration'),
  wordCount('word_count');

  const MeetingSortBy(this.value);

  final String value;

  String get displayName {
    switch (this) {
      case MeetingSortBy.createdAt:
        return '创建时间';
      case MeetingSortBy.updatedAt:
        return '更新时间';
      case MeetingSortBy.title:
        return '标题';
      case MeetingSortBy.duration:
        return '时长';
      case MeetingSortBy.wordCount:
        return '字数';
    }
  }

  @override
  String toString() => value;
}
