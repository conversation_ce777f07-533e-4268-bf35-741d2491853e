// Copyright (c) 2024 VocalMind AI

/// API响应封装类
/// 
/// 统一处理API请求的成功和失败状态
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;
  final int? statusCode;
  
  const ApiResponse._({
    required this.success,
    this.data,
    this.error,
    this.statusCode,
  });
  
  /// 创建成功响应
  factory ApiResponse.success(T data) {
    return ApiResponse._(
      success: true,
      data: data,
    );
  }
  
  /// 创建失败响应
  factory ApiResponse.error(String error, {int? statusCode}) {
    return ApiResponse._(
      success: false,
      error: error,
      statusCode: statusCode,
    );
  }
  
  /// 是否成功
  bool get isSuccess => success;
  
  /// 是否失败
  bool get isError => !success;
  
  /// 获取数据（成功时）
  T? get result => data;
  
  /// 获取错误信息（失败时）
  String get errorMessage => error ?? 'Unknown error';
  
  /// 获取状态码
  int? get code => statusCode;
  
  @override
  String toString() {
    if (success) {
      return 'ApiResponse.success(data: $data)';
    } else {
      return 'ApiResponse.error(error: $error, statusCode: $statusCode)';
    }
  }
}

/// 分页响应数据模型
class PaginatedResponse<T> {
  final List<T> items;
  final int total;
  final int page;
  final int pageSize;
  final bool hasMore;
  
  const PaginatedResponse({
    required this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.hasMore,
  });
  
  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    final itemsJson = json['items'] as List<dynamic>? ?? [];
    final items = itemsJson
        .cast<Map<String, dynamic>>()
        .map(fromJsonT)
        .toList();
    
    final total = json['total'] as int? ?? 0;
    final page = json['page'] as int? ?? 1;
    final pageSize = json['pageSize'] as int? ?? 10;
    final hasMore = json['hasMore'] as bool? ?? false;
    
    return PaginatedResponse(
      items: items,
      total: total,
      page: page,
      pageSize: pageSize,
      hasMore: hasMore,
    );
  }
  
  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'items': items.map(toJsonT).toList(),
      'total': total,
      'page': page,
      'pageSize': pageSize,
      'hasMore': hasMore,
    };
  }
}

/// 基础API错误类
class ApiError implements Exception {
  final String message;
  final int? statusCode;
  final dynamic originalError;
  
  const ApiError(
    this.message, {
    this.statusCode,
    this.originalError,
  });
  
  @override
  String toString() {
    return 'ApiError: $message${statusCode != null ? ' (HTTP $statusCode)' : ''}';
  }
}

/// 网络连接错误
class NetworkError extends ApiError {
  const NetworkError(String message) : super(message);
}

/// 认证错误
class AuthenticationError extends ApiError {
  const AuthenticationError(String message) : super(message, statusCode: 401);
}

/// 权限错误
class AuthorizationError extends ApiError {
  const AuthorizationError(String message) : super(message, statusCode: 403);
}

/// 资源未找到错误
class NotFoundError extends ApiError {
  const NotFoundError(String message) : super(message, statusCode: 404);
}

/// 服务器错误
class ServerError extends ApiError {
  const ServerError(String message, {int? statusCode}) 
      : super(message, statusCode: statusCode ?? 500);
}
