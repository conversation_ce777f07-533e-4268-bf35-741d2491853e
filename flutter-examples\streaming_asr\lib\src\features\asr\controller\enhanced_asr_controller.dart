// Copyright (c) 2024 VocalMind AI
import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:record/record.dart';
import '../../../core/base/base_controller.dart';
import '../../../core/log/app_logger.dart';

import '../model/asr_state.dart';
import '../service/asr_service.dart';
import '../service/audio_service.dart';

/// 增强的ASR控制器
/// 
/// 使用BaseController提供统一的状态管理和错误处理
class EnhancedAsrController extends BaseController<AsrState> {
  final AsrService _asrService;
  final AudioService _audioService;
  
  StreamSubscription<RecordState>? _recordStateSub;
  int _currentIndex = 0;
  bool _isInitialized = false;

  EnhancedAsrController({
    AsrService? asrService,
    AudioService? audioService,
  }) : _asrService = asrService ?? AsrService(),
       _audioService = audioService ?? AudioService(),
       super();

  @override
  String get tag => 'EnhancedAsrController';

  @override
  void onInit() {
    super.onInit();
    setSuccess(AsrState.initial);
    _initializeServices();
  }

  @override
  void onDispose() {
    _cleanup();
    super.onDispose();
  }

  /// 初始化服务
  Future<void> _initializeServices() async {
    await safeExecute(
      () async {
        // 初始化音频服务
        await _audioService.initialize();

        // 监听录音状态变化
        _recordStateSub = _audioService.onStateChanged.listen(_handleRecordStateChange);

        _isInitialized = true;

        return state.data!.copyWith(status: AsrStatus.ready);
      },
      errorMessage: 'Failed to initialize ASR services',
      onSuccess: (newState) {
        AppLogger.info(tag, 'ASR services initialized successfully');
      },
      onError: (error) {
        AppLogger.error(tag, 'Failed to initialize ASR services: $error');
      },
    );
  }

  /// 初始化ASR引擎
  Future<void> initializeAsr() async {
    if (!_isInitialized) {
      await _initializeServices();
    }

    await safeExecute(
      () async {
        // 使用默认配置初始化ASR服务
        const config = AsrConfig.defaultConfig;
        final success = await _asrService.initialize(config);
        if (!success) {
          throw Exception('Failed to initialize ASR engine');
        }
        return state.data!.copyWith(status: AsrStatus.ready);
      },
      errorMessage: 'Failed to initialize ASR engine',
      onSuccess: (newState) {
        AppLogger.info(tag, 'ASR engine initialized successfully');
      },
      onError: (error) {
        AppLogger.error(tag, 'Failed to initialize ASR engine: $error');
      },
    );
  }

  /// 开始录音和识别
  Future<void> startRecording() async {
    final currentState = state.data;
    if (currentState == null || currentState.status != AsrStatus.ready) {
      AppLogger.warning(tag, 'Cannot start recording: ASR not ready');
      return;
    }

    await safeExecute(
      () async {
        // 重置状态
        _currentIndex = 0;

        // 开始录音流
        const config = AsrConfig.defaultConfig;
        final success = await _audioService.startStream(
          config: config,
          onAudioData: _handleAudioData,
        );

        if (!success) {
          throw Exception('Failed to start audio recording');
        }

        return currentState.copyWith(
          status: AsrStatus.recording,
          currentResult: const AsrResult(text: '', isFinal: false, index: 0),
          finalizedResults: [],
        );
      },
      errorMessage: 'Failed to start recording',
      onSuccess: (newState) {
        AppLogger.info(tag, 'Recording started successfully');
      },
      onError: (error) {
        AppLogger.error(tag, 'Failed to start recording: $error');
      },
    );
  }

  /// 停止录音和识别
  Future<void> stopRecording() async {
    final currentState = state.data;
    if (currentState == null || currentState.status != AsrStatus.recording) {
      AppLogger.warning(tag, 'Cannot stop recording: not currently recording');
      return;
    }

    await safeExecute(
      () async {
        // 停止录音
        await _audioService.stop();
        
        // 处理最后的结果
        final finalResults = List<AsrResult>.from(currentState.finalizedResults);
        if (currentState.currentResult.text.isNotEmpty) {
          finalResults.add(currentState.currentResult.copyWith(isFinal: true));
        }
        
        return currentState.copyWith(
          status: AsrStatus.stopped,
          finalizedResults: finalResults,
          currentResult: const AsrResult(text: '', isFinal: false, index: 0),
        );
      },
      errorMessage: 'Failed to stop recording',
      onSuccess: (newState) {
        AppLogger.info(tag, 'Recording stopped successfully');
      },
      onError: (error) {
        AppLogger.error(tag, 'Failed to stop recording: $error');
      },
    );
  }

  /// 重置ASR状态
  void resetAsr() {
    safeSyncExecute(
      () {
        _currentIndex = 0;
        return AsrState.initial.copyWith(status: AsrStatus.ready);
      },
      errorMessage: 'Failed to reset ASR',
      onSuccess: (newState) {
        AppLogger.info(tag, 'ASR reset successfully');
      },
    );
  }

  /// 处理录音状态变化
  void _handleRecordStateChange(RecordState recordState) {
    final currentState = state.data;
    if (currentState == null) return;

    switch (recordState) {
      case RecordState.stop:
        if (currentState.status == AsrStatus.recording) {
          // 录音意外停止，更新状态
          updateData(currentState.copyWith(status: AsrStatus.stopped));
          AppLogger.warning(tag, 'Recording stopped unexpectedly');
        }
        break;
      case RecordState.pause:
        if (currentState.status == AsrStatus.recording) {
          updateData(currentState.copyWith(status: AsrStatus.ready));
          AppLogger.info(tag, 'Recording paused');
        }
        break;
      case RecordState.record:
        // 录音正在进行，无需特殊处理
        break;
    }
  }

  /// 处理音频数据
  void _handleAudioData(List<int> audioData) {
    final currentState = state.data;
    if (currentState == null || currentState.status != AsrStatus.recording) {
      return;
    }

    try {
      // 将音频数据发送给ASR引擎进行识别
      const config = AsrConfig.defaultConfig;
      final result = _asrService.processAudioData(audioData, config.sampleRate);
      if (result != null) {
        _updateAsrResult(result);
      }
    } catch (e) {
      AppLogger.error(tag, 'Error handling audio data', e);
    }
  }

  /// 更新ASR识别结果
  void _updateAsrResult(AsrResult result) {
    final currentState = state.data;
    if (currentState == null) return;

    if (result.isFinal) {
      // 最终结果，添加到已固化列表
      final finalResults = List<AsrResult>.from(currentState.finalizedResults);
      finalResults.add(result.copyWith(index: _currentIndex));
      _currentIndex++;
      
      updateData(currentState.copyWith(
        finalizedResults: finalResults,
        currentResult: const AsrResult(text: '', isFinal: false, index: 0),
      ));
      
      AppLogger.debug(tag, 'Final ASR result: ${result.text}');
    } else {
      // 中间结果，更新当前结果
      updateData(currentState.copyWith(
        currentResult: result.copyWith(index: _currentIndex),
      ));
      
      AppLogger.debug(tag, 'Partial ASR result: ${result.text}');
    }
  }

  /// 清理资源
  void _cleanup() {
    _recordStateSub?.cancel();
    _asrService.dispose();
    _audioService.dispose();
    AppLogger.info(tag, 'ASR controller cleaned up');
  }

  /// 获取当前ASR状态
  AsrState? get currentAsrState => state.data;

  /// 检查是否正在录音
  bool get isRecording => currentAsrState?.status == AsrStatus.recording;

  /// 检查是否已准备就绪
  bool get isReady => currentAsrState?.status == AsrStatus.ready;

  /// 获取完整的识别文本
  String get fullText {
    final currentState = state.data;
    if (currentState == null) return '';
    
    final finalTexts = currentState.finalizedResults
        .map((result) => result.text)
        .where((text) => text.isNotEmpty)
        .join(' ');
    
    final currentText = currentState.currentResult.text;
    
    if (finalTexts.isEmpty) {
      return currentText;
    } else if (currentText.isEmpty) {
      return finalTexts;
    } else {
      return '$finalTexts $currentText';
    }
  }

  /// 获取字数统计
  int get wordCount {
    final text = fullText;
    if (text.isEmpty) return 0;
    return text.trim().split(RegExp(r'\s+')).length;
  }
}

/// Enhanced ASR控制器Provider
final enhancedAsrControllerProvider = StateNotifierProvider<EnhancedAsrController, BaseStateData<AsrState>>((ref) {
  return EnhancedAsrController();
});

/// ASR状态Provider（便捷访问）
final asrStateProvider = Provider<AsrState?>((ref) {
  return ref.watch(enhancedAsrControllerProvider).data;
});

/// 是否正在录音Provider
final isRecordingProvider = Provider<bool>((ref) {
  final asrState = ref.watch(asrStateProvider);
  return asrState?.status == AsrStatus.recording;
});

/// 完整文本Provider
final fullTextProvider = Provider<String>((ref) {
  return ref.watch(enhancedAsrControllerProvider.notifier).fullText;
});

/// 字数统计Provider
final wordCountProvider = Provider<int>((ref) {
  return ref.watch(enhancedAsrControllerProvider.notifier).wordCount;
});
