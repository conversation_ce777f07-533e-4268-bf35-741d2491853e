// Copyright (c) 2024 VocalMind AI
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/app_error.dart';
import '../../core/log/app_logger.dart';

/// 本地存储服务
/// 
/// 提供统一的本地数据存储接口
class StorageService {
  static const String _tag = 'StorageService';
  static StorageService? _instance;
  SharedPreferences? _prefs;

  StorageService._();

  /// 获取单例实例
  static StorageService get instance {
    _instance ??= StorageService._();
    return _instance!;
  }

  /// 初始化存储服务
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      AppLogger.info(_tag, 'Storage service initialized');
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Failed to initialize storage service', e, stackTrace);
      throw AppError.storage(
        'Failed to initialize storage service',
        originalError: e,
      );
    }
  }

  /// 确保已初始化
  void _ensureInitialized() {
    if (_prefs == null) {
      throw AppError.storage('Storage service not initialized');
    }
  }

  /// 存储字符串值
  Future<bool> setString(String key, String value) async {
    try {
      _ensureInitialized();
      final result = await _prefs!.setString(key, value);
      AppLogger.debug(_tag, 'String value stored: $key');
      return result;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to store string value: $key', e);
      throw AppError.storage(
        'Failed to store string value',
        originalError: e,
      );
    }
  }

  /// 获取字符串值
  String? getString(String key, {String? defaultValue}) {
    try {
      _ensureInitialized();
      final value = _prefs!.getString(key) ?? defaultValue;
      AppLogger.debug(_tag, 'String value retrieved: $key');
      return value;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to retrieve string value: $key', e);
      return defaultValue;
    }
  }

  /// 存储整数值
  Future<bool> setInt(String key, int value) async {
    try {
      _ensureInitialized();
      final result = await _prefs!.setInt(key, value);
      AppLogger.debug(_tag, 'Int value stored: $key');
      return result;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to store int value: $key', e);
      throw AppError.storage(
        'Failed to store int value',
        originalError: e,
      );
    }
  }

  /// 获取整数值
  int? getInt(String key, {int? defaultValue}) {
    try {
      _ensureInitialized();
      final value = _prefs!.getInt(key) ?? defaultValue;
      AppLogger.debug(_tag, 'Int value retrieved: $key');
      return value;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to retrieve int value: $key', e);
      return defaultValue;
    }
  }

  /// 存储双精度浮点值
  Future<bool> setDouble(String key, double value) async {
    try {
      _ensureInitialized();
      final result = await _prefs!.setDouble(key, value);
      AppLogger.debug(_tag, 'Double value stored: $key');
      return result;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to store double value: $key', e);
      throw AppError.storage(
        'Failed to store double value',
        originalError: e,
      );
    }
  }

  /// 获取双精度浮点值
  double? getDouble(String key, {double? defaultValue}) {
    try {
      _ensureInitialized();
      final value = _prefs!.getDouble(key) ?? defaultValue;
      AppLogger.debug(_tag, 'Double value retrieved: $key');
      return value;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to retrieve double value: $key', e);
      return defaultValue;
    }
  }

  /// 存储布尔值
  Future<bool> setBool(String key, bool value) async {
    try {
      _ensureInitialized();
      final result = await _prefs!.setBool(key, value);
      AppLogger.debug(_tag, 'Bool value stored: $key');
      return result;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to store bool value: $key', e);
      throw AppError.storage(
        'Failed to store bool value',
        originalError: e,
      );
    }
  }

  /// 获取布尔值
  bool? getBool(String key, {bool? defaultValue}) {
    try {
      _ensureInitialized();
      final value = _prefs!.getBool(key) ?? defaultValue;
      AppLogger.debug(_tag, 'Bool value retrieved: $key');
      return value;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to retrieve bool value: $key', e);
      return defaultValue;
    }
  }

  /// 存储字符串列表
  Future<bool> setStringList(String key, List<String> value) async {
    try {
      _ensureInitialized();
      final result = await _prefs!.setStringList(key, value);
      AppLogger.debug(_tag, 'String list stored: $key');
      return result;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to store string list: $key', e);
      throw AppError.storage(
        'Failed to store string list',
        originalError: e,
      );
    }
  }

  /// 获取字符串列表
  List<String>? getStringList(String key, {List<String>? defaultValue}) {
    try {
      _ensureInitialized();
      final value = _prefs!.getStringList(key) ?? defaultValue;
      AppLogger.debug(_tag, 'String list retrieved: $key');
      return value;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to retrieve string list: $key', e);
      return defaultValue;
    }
  }

  /// 存储JSON对象
  Future<bool> setJson(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = jsonEncode(value);
      return await setString(key, jsonString);
    } catch (e) {
      AppLogger.error(_tag, 'Failed to store JSON object: $key', e);
      throw AppError.storage(
        'Failed to store JSON object',
        originalError: e,
      );
    }
  }

  /// 获取JSON对象
  Map<String, dynamic>? getJson(String key, {Map<String, dynamic>? defaultValue}) {
    try {
      final jsonString = getString(key);
      if (jsonString == null) return defaultValue;
      
      final decoded = jsonDecode(jsonString);
      if (decoded is Map<String, dynamic>) {
        return decoded;
      }
      return defaultValue;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to retrieve JSON object: $key', e);
      return defaultValue;
    }
  }

  /// 存储对象（通过JSON序列化）
  Future<bool> setObject<T>(
    String key,
    T object,
    Map<String, dynamic> Function(T) toJson,
  ) async {
    try {
      final jsonMap = toJson(object);
      return await setJson(key, jsonMap);
    } catch (e) {
      AppLogger.error(_tag, 'Failed to store object: $key', e);
      throw AppError.storage(
        'Failed to store object',
        originalError: e,
      );
    }
  }

  /// 获取对象（通过JSON反序列化）
  T? getObject<T>(
    String key,
    T Function(Map<String, dynamic>) fromJson, {
    T? defaultValue,
  }) {
    try {
      final jsonMap = getJson(key);
      if (jsonMap == null) return defaultValue;
      
      return fromJson(jsonMap);
    } catch (e) {
      AppLogger.error(_tag, 'Failed to retrieve object: $key', e);
      return defaultValue;
    }
  }

  /// 检查键是否存在
  bool containsKey(String key) {
    try {
      _ensureInitialized();
      return _prefs!.containsKey(key);
    } catch (e) {
      AppLogger.error(_tag, 'Failed to check key existence: $key', e);
      return false;
    }
  }

  /// 删除键值对
  Future<bool> remove(String key) async {
    try {
      _ensureInitialized();
      final result = await _prefs!.remove(key);
      AppLogger.debug(_tag, 'Key removed: $key');
      return result;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to remove key: $key', e);
      throw AppError.storage(
        'Failed to remove key',
        originalError: e,
      );
    }
  }

  /// 清除所有数据
  Future<bool> clear() async {
    try {
      _ensureInitialized();
      final result = await _prefs!.clear();
      AppLogger.info(_tag, 'All storage data cleared');
      return result;
    } catch (e) {
      AppLogger.error(_tag, 'Failed to clear storage', e);
      throw AppError.storage(
        'Failed to clear storage',
        originalError: e,
      );
    }
  }

  /// 获取所有键
  Set<String> getKeys() {
    try {
      _ensureInitialized();
      return _prefs!.getKeys();
    } catch (e) {
      AppLogger.error(_tag, 'Failed to get all keys', e);
      return <String>{};
    }
  }

  /// 重新加载数据
  Future<void> reload() async {
    try {
      _ensureInitialized();
      await _prefs!.reload();
      AppLogger.debug(_tag, 'Storage data reloaded');
    } catch (e) {
      AppLogger.error(_tag, 'Failed to reload storage', e);
      throw AppError.storage(
        'Failed to reload storage',
        originalError: e,
      );
    }
  }
}

/// 存储键常量
class StorageKeys {
  // 用户相关
  static const String userInfo = 'user_info';
  static const String authToken = 'auth_token';
  static const String userPreferences = 'user_preferences';
  
  // 应用设置
  static const String appSettings = 'app_settings';
  static const String serverConfig = 'server_config';
  static const String llmConfig = 'llm_config';
  
  // 功能相关
  static const String asrSettings = 'asr_settings';
  static const String lastMeetingId = 'last_meeting_id';
  static const String todoSettings = 'todo_settings';
  
  // 缓存
  static const String meetingCache = 'meeting_cache';
  static const String aiChatCache = 'ai_chat_cache';
  
  // 其他
  static const String firstLaunch = 'first_launch';
  static const String lastVersion = 'last_version';
  static const String crashReports = 'crash_reports';
}
