// Copyright (c) 2024 VocalMind AI
import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../../core/log/app_logger.dart';
import '../../../shared/model/app_error.dart';
import '../model/meeting_models.dart';

/// 会议记录数据库服务
/// 
/// 管理会议记录的本地存储
class MeetingDatabaseService {
  static const String _tag = 'MeetingDatabaseService';
  static const String _databaseName = 'meetings.db';
  static const int _databaseVersion = 1;
  
  static const String _tableMeetings = 'meetings';
  static const String _tableTags = 'tags';
  static const String _tableMeetingTags = 'meeting_tags';

  static MeetingDatabaseService? _instance;
  Database? _database;

  MeetingDatabaseService._();

  /// 获取单例实例
  static MeetingDatabaseService get instance {
    _instance ??= MeetingDatabaseService._();
    return _instance!;
  }

  /// 初始化数据库
  Future<void> initialize() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);

      _database = await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
      );

      AppLogger.info(_tag, 'Database initialized successfully');
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Failed to initialize database', e, stackTrace);
      throw AppError.storage(
        'Failed to initialize meeting database',
        originalError: e,
      );
    }
  }

  /// 创建数据库表
  Future<void> _onCreate(Database db, int version) async {
    // 创建会议记录表
    await db.execute('''
      CREATE TABLE $_tableMeetings (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        original_content TEXT NOT NULL,
        optimized_content TEXT,
        summary TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        duration INTEGER DEFAULT 0,
        word_count INTEGER DEFAULT 0,
        speaker_count INTEGER DEFAULT 1,
        audio_file_path TEXT,
        status TEXT DEFAULT 'draft',
        metadata TEXT
      )
    ''');

    // 创建标签表
    await db.execute('''
      CREATE TABLE $_tableTags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        color TEXT,
        created_at TEXT NOT NULL
      )
    ''');

    // 创建会议-标签关联表
    await db.execute('''
      CREATE TABLE $_tableMeetingTags (
        meeting_id TEXT NOT NULL,
        tag_name TEXT NOT NULL,
        PRIMARY KEY (meeting_id, tag_name),
        FOREIGN KEY (meeting_id) REFERENCES $_tableMeetings (id) ON DELETE CASCADE,
        FOREIGN KEY (tag_name) REFERENCES $_tableTags (name) ON DELETE CASCADE
      )
    ''');

    // 创建索引
    await db.execute('CREATE INDEX idx_meetings_created_at ON $_tableMeetings (created_at)');
    await db.execute('CREATE INDEX idx_meetings_updated_at ON $_tableMeetings (updated_at)');
    await db.execute('CREATE INDEX idx_meetings_status ON $_tableMeetings (status)');
    await db.execute('CREATE INDEX idx_meetings_title ON $_tableMeetings (title)');

    AppLogger.info(_tag, 'Database tables created successfully');
  }

  /// 升级数据库
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    AppLogger.info(_tag, 'Upgrading database from $oldVersion to $newVersion');
    // TODO: 实现数据库升级逻辑
  }

  /// 确保数据库已初始化
  void _ensureInitialized() {
    if (_database == null) {
      throw AppError.storage('Database not initialized');
    }
  }

  /// 插入会议记录
  Future<String> insertMeeting(MeetingRecord meeting) async {
    try {
      _ensureInitialized();
      
      await _database!.transaction((txn) async {
        // 插入会议记录
        await txn.insert(
          _tableMeetings,
          _meetingToMap(meeting),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
        
        // 插入标签关联
        await _insertMeetingTags(txn, meeting.id, meeting.tags);
      });
      
      AppLogger.debug(_tag, 'Meeting inserted: ${meeting.id}');
      return meeting.id;
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Failed to insert meeting', e, stackTrace);
      throw AppError.storage(
        'Failed to save meeting record',
        originalError: e,
      );
    }
  }

  /// 更新会议记录
  Future<void> updateMeeting(MeetingRecord meeting) async {
    try {
      _ensureInitialized();
      
      await _database!.transaction((txn) async {
        // 更新会议记录
        final count = await txn.update(
          _tableMeetings,
          _meetingToMap(meeting),
          where: 'id = ?',
          whereArgs: [meeting.id],
        );
        
        if (count == 0) {
          throw AppError.notFound('Meeting not found: ${meeting.id}');
        }
        
        // 删除旧的标签关联
        await txn.delete(
          _tableMeetingTags,
          where: 'meeting_id = ?',
          whereArgs: [meeting.id],
        );
        
        // 插入新的标签关联
        await _insertMeetingTags(txn, meeting.id, meeting.tags);
      });
      
      AppLogger.debug(_tag, 'Meeting updated: ${meeting.id}');
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Failed to update meeting', e, stackTrace);
      throw AppError.storage(
        'Failed to update meeting record',
        originalError: e,
      );
    }
  }

  /// 删除会议记录
  Future<void> deleteMeeting(String id) async {
    try {
      _ensureInitialized();
      
      final count = await _database!.delete(
        _tableMeetings,
        where: 'id = ?',
        whereArgs: [id],
      );
      
      if (count == 0) {
        throw AppError.notFound('Meeting not found: $id');
      }
      
      AppLogger.debug(_tag, 'Meeting deleted: $id');
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Failed to delete meeting', e, stackTrace);
      throw AppError.storage(
        'Failed to delete meeting record',
        originalError: e,
      );
    }
  }

  /// 根据ID获取会议记录
  Future<MeetingRecord?> getMeetingById(String id) async {
    try {
      _ensureInitialized();
      
      final List<Map<String, dynamic>> maps = await _database!.query(
        _tableMeetings,
        where: 'id = ?',
        whereArgs: [id],
      );
      
      if (maps.isEmpty) {
        return null;
      }
      
      final meetingMap = maps.first;
      final tags = await _getMeetingTags(id);
      
      return _mapToMeeting(meetingMap, tags);
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Failed to get meeting by id', e, stackTrace);
      throw AppError.storage(
        'Failed to retrieve meeting record',
        originalError: e,
      );
    }
  }

  /// 获取会议记录列表
  Future<List<MeetingRecord>> getMeetings({
    MeetingFilter? filter,
    int? limit,
    int? offset,
  }) async {
    try {
      _ensureInitialized();
      
      String sql = 'SELECT * FROM $_tableMeetings';
      List<dynamic> args = [];
      
      // 构建WHERE子句
      final whereConditions = <String>[];
      
      if (filter != null) {
        if (filter.keyword != null && filter.keyword!.isNotEmpty) {
          whereConditions.add('(title LIKE ? OR original_content LIKE ? OR optimized_content LIKE ?)');
          final keyword = '%${filter.keyword}%';
          args.addAll([keyword, keyword, keyword]);
        }
        
        if (filter.status != null) {
          whereConditions.add('status = ?');
          args.add(filter.status!.value);
        }
        
        if (filter.startDate != null) {
          whereConditions.add('created_at >= ?');
          args.add(filter.startDate!.toIso8601String());
        }
        
        if (filter.endDate != null) {
          whereConditions.add('created_at <= ?');
          args.add(filter.endDate!.toIso8601String());
        }
        
        if (filter.minDuration != null) {
          whereConditions.add('duration >= ?');
          args.add(filter.minDuration);
        }
        
        if (filter.maxDuration != null) {
          whereConditions.add('duration <= ?');
          args.add(filter.maxDuration);
        }
      }
      
      if (whereConditions.isNotEmpty) {
        sql += ' WHERE ${whereConditions.join(' AND ')}';
      }
      
      // 构建ORDER BY子句
      if (filter?.sortBy != null) {
        sql += ' ORDER BY ${filter!.sortBy.value}';
        sql += filter.ascending ? ' ASC' : ' DESC';
      } else {
        sql += ' ORDER BY created_at DESC';
      }
      
      // 添加LIMIT和OFFSET
      if (limit != null) {
        sql += ' LIMIT $limit';
        if (offset != null) {
          sql += ' OFFSET $offset';
        }
      }
      
      final List<Map<String, dynamic>> maps = await _database!.rawQuery(sql, args);
      
      final meetings = <MeetingRecord>[];
      for (final map in maps) {
        final tags = await _getMeetingTags(map['id'] as String);
        meetings.add(_mapToMeeting(map, tags));
      }
      
      return meetings;
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Failed to get meetings', e, stackTrace);
      throw AppError.storage(
        'Failed to retrieve meeting records',
        originalError: e,
      );
    }
  }

  /// 获取会议记录总数
  Future<int> getMeetingCount({MeetingFilter? filter}) async {
    try {
      _ensureInitialized();
      
      String sql = 'SELECT COUNT(*) FROM $_tableMeetings';
      List<dynamic> args = [];
      
      // 构建WHERE子句（与getMeetings相同的逻辑）
      final whereConditions = <String>[];
      
      if (filter != null) {
        if (filter.keyword != null && filter.keyword!.isNotEmpty) {
          whereConditions.add('(title LIKE ? OR original_content LIKE ? OR optimized_content LIKE ?)');
          final keyword = '%${filter.keyword}%';
          args.addAll([keyword, keyword, keyword]);
        }
        
        if (filter.status != null) {
          whereConditions.add('status = ?');
          args.add(filter.status!.value);
        }
        
        if (filter.startDate != null) {
          whereConditions.add('created_at >= ?');
          args.add(filter.startDate!.toIso8601String());
        }
        
        if (filter.endDate != null) {
          whereConditions.add('created_at <= ?');
          args.add(filter.endDate!.toIso8601String());
        }
        
        if (filter.minDuration != null) {
          whereConditions.add('duration >= ?');
          args.add(filter.minDuration);
        }
        
        if (filter.maxDuration != null) {
          whereConditions.add('duration <= ?');
          args.add(filter.maxDuration);
        }
      }
      
      if (whereConditions.isNotEmpty) {
        sql += ' WHERE ${whereConditions.join(' AND ')}';
      }
      
      final result = await _database!.rawQuery(sql, args);
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e, stackTrace) {
      AppLogger.error(_tag, 'Failed to get meeting count', e, stackTrace);
      throw AppError.storage(
        'Failed to count meeting records',
        originalError: e,
      );
    }
  }

  /// 插入会议标签关联
  Future<void> _insertMeetingTags(Transaction txn, String meetingId, List<String> tags) async {
    for (final tag in tags) {
      // 确保标签存在
      await txn.insert(
        _tableTags,
        {
          'name': tag,
          'created_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.ignore,
      );
      
      // 插入关联
      await txn.insert(
        _tableMeetingTags,
        {
          'meeting_id': meetingId,
          'tag_name': tag,
        },
        conflictAlgorithm: ConflictAlgorithm.ignore,
      );
    }
  }

  /// 获取会议的标签
  Future<List<String>> _getMeetingTags(String meetingId) async {
    final List<Map<String, dynamic>> maps = await _database!.query(
      _tableMeetingTags,
      columns: ['tag_name'],
      where: 'meeting_id = ?',
      whereArgs: [meetingId],
    );
    
    return maps.map((map) => map['tag_name'] as String).toList();
  }

  /// 会议记录转Map
  Map<String, dynamic> _meetingToMap(MeetingRecord meeting) {
    final map = meeting.toJson();
    // 移除tags字段，因为它们存储在单独的表中
    map.remove('tags');
    return map;
  }

  /// Map转会议记录
  MeetingRecord _mapToMeeting(Map<String, dynamic> map, List<String> tags) {
    final meetingMap = Map<String, dynamic>.from(map);
    meetingMap['tags'] = tags;
    return MeetingRecord.fromJson(meetingMap);
  }

  /// 关闭数据库
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      AppLogger.info(_tag, 'Database closed');
    }
  }
}
