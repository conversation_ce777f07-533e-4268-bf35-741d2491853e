// Copyright (c)  2024  Xiaomi Corporation
import 'package:flutter/foundation.dart';
import 'package:sherpa_onnx/sherpa_onnx.dart' as sherpa_onnx;
import '../model/asr_state.dart';
import '../../../core/utils/audio_utils.dart';
import 'model_service.dart';

/// ASR核心服务
/// 
/// 封装sherpa_onnx的调用，包括初始化、创建识别器、处理音频流等
class AsrService {
  sherpa_onnx.OnlineRecognizer? _recognizer;
  sherpa_onnx.OnlineStream? _stream;
  bool _isInitialized = false;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 初始化ASR系统
  /// 
  /// [config] ASR配置
  /// 
  /// 返回是否初始化成功
  Future<bool> initialize(AsrConfig config) async {
    try {
      if (_isInitialized) {
        return true;
      }

      // 初始化sherpa-onnx绑定
      sherpa_onnx.initBindings();

      // 创建识别器
      _recognizer = await _createOnlineRecognizer(config);
      
      // 创建音频流
      _stream = _recognizer?.createStream();

      _isInitialized = true;
      debugPrint('ASR system initialized successfully');
      return true;
    } catch (e) {
      debugPrint('Failed to initialize ASR system: $e');
      return false;
    }
  }

  /// 创建在线识别器
  Future<sherpa_onnx.OnlineRecognizer> _createOnlineRecognizer(
      AsrConfig config) async {
    final modelConfig = await ModelService.getOnlineModelConfig(
      type: config.modelType,
    );
    
    final recognizerConfig = sherpa_onnx.OnlineRecognizerConfig(
      model: modelConfig,
      ruleFsts: '',
    );

    return sherpa_onnx.OnlineRecognizer(recognizerConfig);
  }

  /// 处理音频数据
  ///
  /// [audioData] 音频数据（Int16 PCM格式）
  /// [sampleRate] 采样率
  ///
  /// 返回识别结果，如果没有结果则返回null
  AsrResult? processAudioData(List<int> audioData, int sampleRate) {
    if (!_isInitialized || _recognizer == null || _stream == null) {
      debugPrint('ASR system not initialized');
      return null;
    }

    try {
      // 转换音频格式
      final samplesFloat32 = convertBytesToFloat32(Uint8List.fromList(audioData));

      // 送入音频数据
      _stream!.acceptWaveform(
        samples: samplesFloat32,
        sampleRate: sampleRate,
      );

      // 处理音频数据
      while (_recognizer!.isReady(_stream!)) {
        _recognizer!.decode(_stream!);
      }

      // 获取识别结果
      final result = _recognizer!.getResult(_stream!);
      final text = result.text;

      if (text.isNotEmpty) {
        return AsrResult(
          text: text,
          isFinal: false,
          index: 0, // 这里的index会在应用层管理
        );
      }

      return null;
    } catch (e) {
      debugPrint('Failed to process audio data: $e');
      return null;
    }
  }

  /// 检查是否到达端点（一句话结束）
  bool isEndpoint() {
    if (!_isInitialized || _recognizer == null || _stream == null) {
      return false;
    }

    try {
      return _recognizer!.isEndpoint(_stream!);
    } catch (e) {
      debugPrint('Failed to check endpoint: $e');
      return false;
    }
  }

  /// 重置识别流（准备识别下一句话）
  void resetStream() {
    if (!_isInitialized || _recognizer == null || _stream == null) {
      return;
    }

    try {
      _recognizer!.reset(_stream!);
    } catch (e) {
      debugPrint('Failed to reset stream: $e');
    }
  }

  /// 创建新的识别流
  void createNewStream() {
    if (!_isInitialized || _recognizer == null) {
      return;
    }

    try {
      _stream?.free();
      _stream = _recognizer!.createStream();
    } catch (e) {
      debugPrint('Failed to create new stream: $e');
    }
  }

  /// 释放资源
  void dispose() {
    try {
      _stream?.free();
      _recognizer?.free();
      _stream = null;
      _recognizer = null;
      _isInitialized = false;
      debugPrint('ASR service disposed');
    } catch (e) {
      debugPrint('Failed to dispose ASR service: $e');
    }
  }
}
