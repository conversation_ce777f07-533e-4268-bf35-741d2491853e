name: streaming_asr

description: >
  This example shows how to implement real-time speech recognition using sherpa-onnx.

publish_to: 'none'

version: 1.12.7

topics:
  - speech-recognition

issue_tracker: https://github.com/k2-fsa/sherpa-onnx/issues

repository: https://github.com/k2-fsa/sherpa-onnx/tree/master/sherpa-onnx/flutter

environment:
  sdk: ">=2.17.0 <4.0.0"
  flutter: ">=2.8.1"

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.6

  # 路径和文件处理
  path_provider: ^2.1.3
  path: ^1.9.0

  # 音频录制和播放
  record: ^5.1.0
  audioplayers: ^6.0.0

  # 网络请求
  dio: ^5.4.0

  # 本地存储
  sqflite: ^2.3.0
  shared_preferences: ^2.2.2

  # 通知和后台服务
  flutter_local_notifications: ^17.0.0
  flutter_background_service: ^5.0.5

  # 权限管理
  permission_handler: ^11.3.0

  # 文件选择
  file_picker: ^8.0.0

  # 日期时间格式化
  intl: ^0.19.0

  # 其他工具
  url_launcher: ^6.2.6
  uuid: ^4.3.3

  # 核心框架
  sherpa_onnx: ^1.12.7
  flutter_riverpod: ^2.6.1
  go_router: ^16.1.0
  # sherpa_onnx:
  #   path: ../../flutter/sherpa_onnx

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/decoder-epoch-99-avg-1.onnx
    - assets/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/encoder-epoch-99-avg-1.int8.onnx
    - assets/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/joiner-epoch-99-avg-1.int8.onnx
    - assets/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/joiner-epoch-99-avg-1.onnx
    - assets/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/tokens.txt

    #    - assets/
    # - assets/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/
