# Android 应用功能模块与执行流程

本文档旨在概述 VocalMind AI Android 应用的主要功能模块，并详细描述其核心代码的执行流程。

## 一、 主要功能模块

根据对项目结构和 `AndroidManifest.xml` 的分析，应用主要包含以下几个功能模块：

1.  **用户认证模块**:
    *   **功能**: 提供完整的用户账户体系，包括注册、登录、忘记密码等。
    *   **相关组件**: `WelcomeActivity`, `LoginActivity`, `RegisterActivity`, `ForgotPasswordActivity`。

2.  **实时会议转录模块**:
    *   **功能**: 应用的核心功能。利用 `sherpa-onnx` 模型库，通过麦克风实时捕捉音频，并将其转换为文字显示在屏幕上。
    *   **相关组件**: `SingleModelActivity` (主交互界面), `AudioRecordingService` (后台录音服务), `sherpa-onnx` (底层 ASR 引擎)。

3.  **会议记录管理模块**:
    *   **功能**: 保存、展示和管理历史会议的转录记录。用户可以随时回顾和查看过往的会议内容。
    *   **相关组件**: `MeetingRecordsActivity` (列表页), `MeetingDetailActivity` (详情页)。

4.  **AI 交互模块**:
    *   **功能**: 在会议详情中，提供一个与 AI 聊天的界面，允许用户基于当前会议内容进行提问、总结或获取关键信息。
    *   **相关组件**: `AiChatActivity`。

5.  **AI 待办事项 (Todo) 模块**:
    *   **功能**: 一个智能待办事项系统，可能支持通过语音创建待办，并能在指定时间通过系统通知和铃声进行提醒。
    *   **相关组件**: `TodoActivity`, `TodoEditActivity`, `TodoReminderReceiver`, `BootReceiver`。

6.  **后台服务与悬浮窗模块**:
    *   **功能**: 确保应用即使在后台或切换到其他应用时也能持续进行录音。悬浮窗提供了一个快捷的控制入口（如开始/停止录音）。
    *   **相关组件**: `AudioRecordingService`, `FloatingWindowService`。

7.  **设置模块**:
    *   **功能**: 允许用户对应用进行个性化配置，包括对大语言模型 (LLM) 和服务器的参数设置。
    *   **相关组件**: `SettingsActivity`, `ServerSettingsActivity`。

## 二、 核心执行流程 (实时会议转录)

以下是用户从启动应用到完成一次实时会议转录并保存的核心流程。
### 流程图 (Mermaid)

```mermaid
graph TD
    subgraph 用户界面 (Activity)
        A[用户打开应用] --> B{检查/请求权限};
        B -- 权限通过 --> C[进入 SingleModelActivity];
        C -- 点击 "开始录音" --> D[启动 AudioRecordingService];
        D --> E{UI 更新为 "录音中..."};
        E --> F[实时显示 ASR 结果];
        C -- 点击 "停止录音" --> G[停止 AudioRecordingService];
        G --> H{UI 更新为 "空闲"};
        H --> I[保存会议记录];
    end

    subgraph 后台服务 (Service)
        D -- 意图(Intent) --> J[AudioRecordingService.onStartCommand];
        J --> K[初始化 SherpaOnnx ASR 引擎];
        K --> L[创建 AudioRecord 实例];
        L --> M[开始录制麦克风音频];
        M -- 音频数据流 --> N[持续将音频 Buffer 传入 ASR 引擎];
        N -- 识别结果 --> O[通过回调/广播更新 UI];
        O --> F;
        G -- 意图(Intent) --> P[AudioRecordingService.onDestroy];
        P --> Q[停止 AudioRecord];
        Q --> R[释放 ASR 引擎资源];
    end

    subgraph 底层引擎 (JNI/sherpa-onnx)
        K --> S[加载 ASR 模型和词汇表];
        N --> T[引擎处理音频流并进行解码];
        T --> N;
    end

    style F fill:#c9ffc9,stroke:#333,stroke-width:2px
    style O fill:#c9ffc9,stroke:#333,stroke-width:2px
```

### 流程步骤详解

1.  **启动与授权:**
    *   用户打开应用, 首先进入 `WelcomeActivity` 或 `SingleModelActivity`.
    *   应用检查 `RECORD_AUDIO` (录音) 等关键权限. 如果未授权, 会提示用户授权.

2.  **开始识别:**
    *   在主界面 (`SingleModelActivity`), 用户点击 "开始录音" 按钮.
    *   UI 发出一个 `Intent` 来启动 `AudioRecordingService`. 为了确保录音在应用退至后台时也能继续, 该服务会以**前台服务 (Foreground Service)** 的形式启动.
    *   UI 状态随之更新, 例如按钮变为 "停止", 并显示 "正在识别..." 的提示.

3.  **后台处理:**
    *   `AudioRecordingService` 接收到启动命令后开始执行.
    *   服务内部初始化 `sherpa-onnx` 的 ASR 引擎实例, 加载所需的模型文件 (通常来自 `assets` 目录).
    *   创建一个 `AudioRecord` 对象, 配置好音频源, 采样率等参数, 并开始从麦克风捕获音频数据.
    *   服务在一个独立的线程中, 将捕获到的音频数据块 (Buffer) 持续送入 ASR 引擎进行处理.

4.  **结果反馈:**
    *   ASR 引擎实时处理音频流, 并将识别出的文本结果 (部分或最终结果) 通过回调函数或广播 `Intent` 的方式返回给 `AudioRecordingService`.
    *   `AudioRecordingService` 再将这些文本结果传递给当前的 `SingleModelActivity`.
    *   Activity 收到结果后, 在主线程上更新界面中的 `TextView`, 让用户能够实时看到语音转换的文本.

5.  **停止与保存:**
    *   用户点击 "停止录音" 按钮.
    *   UI 发送一个 `Intent` 来停止 `AudioRecordingService`.
    *   服务接收到停止命令后, 会停止 `AudioRecord` 的录制, 并调用 ASR 引擎的 `reset` 或 `release` 方法来清理和释放底层资源.
    *   服务停止后, 应用会将本次识别的完整文本内容整理并保存到数据库或文件中, 成为一条新的 "会议记录".
    *   UI 恢复到初始的空闲状态.