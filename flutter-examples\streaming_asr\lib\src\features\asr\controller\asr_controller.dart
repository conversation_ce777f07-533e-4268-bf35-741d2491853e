// Copyright (c)  2024  Xiaomi Corporation
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:record/record.dart';
import '../model/asr_state.dart';
import '../service/asr_service.dart';
import '../service/audio_service.dart';

/// ASR控制器
/// 
/// 管理ASR的整个生命周期，包括初始化、开始/停止录音、处理音频流数据等
class AsrController extends Notifier<AsrState> {
  late final AsrService _asrService;
  late final AudioService _audioService;
  StreamSubscription<RecordState>? _recordStateSub;
  int _currentIndex = 0;

  @override
  AsrState build() {
    _asrService = AsrService();
    _audioService = AudioService();
    
    // 监听录音状态变化
    _initializeAudioService();
    
    return AsrState.initial;
  }

  /// 初始化音频服务
  Future<void> _initializeAudioService() async {
    await _audioService.initialize();
    
    _recordStateSub = _audioService.onStateChanged.listen((recordState) {
      // 根据录音状态更新ASR状态
      if (recordState == RecordState.stop && state.status == AsrStatus.recording) {
        state = state.copyWith(status: AsrStatus.ready);
      }
    });
  }

  /// 初始化ASR系统
  Future<void> initializeAsr() async {
    if (state.status != AsrStatus.uninitialized) {
      return;
    }

    state = state.copyWith(status: AsrStatus.ready);

    try {
      final success = await _asrService.initialize(state.config);
      if (success) {
        state = state.copyWith(status: AsrStatus.ready);
        debugPrint('ASR system initialized successfully');
      } else {
        state = state.copyWith(
          status: AsrStatus.error,
          errorMessage: 'Failed to initialize ASR system',
        );
      }
    } catch (e) {
      state = state.copyWith(
        status: AsrStatus.error,
        errorMessage: 'ASR initialization error: $e',
      );
    }
  }

  /// 开始录音和识别
  Future<void> startRecording() async {
    if (state.status == AsrStatus.recording) {
      return;
    }

    // 如果未初始化，先初始化
    if (state.status == AsrStatus.uninitialized) {
      await initializeAsr();
      if (state.status != AsrStatus.ready) {
        return;
      }
    }

    try {
      final success = await _audioService.startStream(
        config: state.config,
        onAudioData: _handleAudioData,
      );

      if (success) {
        state = state.copyWith(status: AsrStatus.recording);
        debugPrint('Started recording and recognition');
      } else {
        state = state.copyWith(
          status: AsrStatus.error,
          errorMessage: 'Failed to start audio recording',
        );
      }
    } catch (e) {
      state = state.copyWith(
        status: AsrStatus.error,
        errorMessage: 'Recording start error: $e',
      );
    }
  }

  /// 停止录音和识别
  Future<void> stopRecording() async {
    if (state.status != AsrStatus.recording) {
      return;
    }

    try {
      await _audioService.stop();
      _asrService.createNewStream();
      
      state = state.copyWith(status: AsrStatus.ready);
      debugPrint('Stopped recording and recognition');
    } catch (e) {
      state = state.copyWith(
        status: AsrStatus.error,
        errorMessage: 'Recording stop error: $e',
      );
    }
  }

  /// 处理音频数据
  void _handleAudioData(List<int> audioData) {
    if (state.status != AsrStatus.recording) {
      return;
    }

    try {
      // 处理音频数据获取识别结果
      final result = _asrService.processAudioData(
        audioData,
        state.config.sampleRate,
      );

      if (result != null) {
        // 更新当前结果
        final updatedResult = result.copyWith(index: _currentIndex);
        state = state.copyWith(currentResult: updatedResult);

        // 检查是否到达端点
        if (_asrService.isEndpoint()) {
          _finalizeCurrentResult();
        }
      }
    } catch (e) {
      debugPrint('Error processing audio data: $e');
      state = state.copyWith(
        status: AsrStatus.error,
        errorMessage: 'Audio processing error: $e',
      );
    }
  }

  /// 固化当前识别结果
  void _finalizeCurrentResult() {
    if (state.currentResult.text.isNotEmpty) {
      final finalResult = state.currentResult.copyWith(isFinal: true);
      final updatedFinalizedResults = [...state.finalizedResults, finalResult];
      
      _currentIndex++;
      
      state = state.copyWith(
        finalizedResults: updatedFinalizedResults,
        currentResult: const AsrResult(
          text: '',
          isFinal: false,
          index: 0,
        ).copyWith(index: _currentIndex),
      );

      // 重置识别流
      _asrService.resetStream();
    }
  }

  /// 清除所有结果
  void clearResults() {
    _currentIndex = 0;
    state = state.copyWith(
      currentResult: const AsrResult(text: '', isFinal: false, index: 0),
      finalizedResults: [],
    );
  }

  /// 更新配置
  void updateConfig(AsrConfig newConfig) {
    if (state.status == AsrStatus.recording) {
      debugPrint('Cannot update config while recording');
      return;
    }

    state = state.copyWith(config: newConfig);
  }

  /// 清除错误状态
  void clearError() {
    if (state.status == AsrStatus.error) {
      state = state.copyWith(
        status: AsrStatus.ready,
        errorMessage: null,
      );
    }
  }

  /// 释放资源
  void disposeResources() {
    _recordStateSub?.cancel();
    _audioService.dispose();
    _asrService.dispose();
  }
}

/// ASR控制器Provider实例
final asrControllerProvider = NotifierProvider<AsrController, AsrState>(() {
  return AsrController();
});
