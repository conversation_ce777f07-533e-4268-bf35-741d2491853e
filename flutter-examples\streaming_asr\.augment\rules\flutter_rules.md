---
type: "always_apply"
---

# 代码规范和项目结构

本文档旨在为 `flutter` 项目提供一套标准的开发规范和架构指南。遵循这些准则有助于保持代码库的整洁、可维护和可扩展。

## 核心原则

*   **三层结构 (Three-Tier Architecture)**: 代码按职责分为三个明确的顶级目录: `core`, `shared`, 和 `features`。
*   **分层设计 (Layered Design)**: 在 `shared` 和 `features` 内部, 代码按 `ui`, `controller`, `model`, `service` 进一步分层。
*   **清晰的依赖关系 (Clear Dependency Rule)**: 依赖方向是单向的: `features` → `shared` → `core`。下层永远不能依赖上层。
*   **响应式状态管理 (Reactive State Management)**: 使用 `Riverpod` 作为状态管理库, 实现单向数据流和响应式UI更新。

## 1. 顶级目录结构

项目 `lib/src` 目录遵循以下顶级结构：

```
lib/
└── src/
    ├── core/         # 核心框架层 (完全业务无关)
    ├── shared/       # 跨功能共享业务层
    └── features/     # 独立功能层
```

### 1.1. `core` 层

*   **职责**: 存放应用的核心框架代码, **必须完全独立于任何业务逻辑**。
*   **示例**: API客户端封装、日志服务、自定义的基类 (`BaseController`)、框架扩展。
*   **规则**: `core` 层的代码是整个应用最底层的支撑, 它不知道 `shared` 和 `features` 的存在。
*   **内部结构**: `core` 目录内部应根据其提供的框架功能进行组织, 创建对应的子目录。
    ```
    core/
    ├── api/          # API客户端封装
    ├── base/         # 抽象基类 (如 BaseController)
    ├── extensions/   # Dart语言扩展
    ├── log/          # 日志服务
    ├── router/       # 导航路由
    └── utils/        # 通用工具函数
    ```

### 1.2. `shared` 层

*   **职责**: 存放被**多个功能 (`features`)** 使用的、**与业务相关的**共享代码。
*   **规则**: 当你发现某个组件或逻辑需要在多个功能模块间复用时, 就应该把它放到 `shared` 层。`shared` 层的代码可以依赖 `core` 层, 但不知道任何具体 `feature` 的存在。
*   **内部结构**: `shared` 层内部也应该遵循分层设计, 建立自己的 `ui`, `controller`, `model`, `service` 目录。

### 1.3. `features` 层

*   **职责**: 存放具体的、独立的业务功能模块 (例如: ASR语音识别、用户设置、历史记录)。
*   **规则**: 每个 `feature` 都是一个相对独立的“小应用”。它内部的代码不应该被其他 `feature` 直接导入。如果需要共享, 应将代码提升到 `shared` 层。`features` 可以依赖 `shared` 和 `core` 层。
*   **内部结构**: 每个 `feature` 内部都遵循相同的分层设计。

## 2. 内部分层详解

在 `features` 和 `shared` 目录内部, 我们遵循一致的分层模型:

```
# features/asr/ 或 shared/
├── controller/  # 控制层: 业务逻辑
├── model/       # 模型层: 数据模型/状态
├── ui/          # UI层: UI Widgets
└── service/     # 服务层: 封装外部依赖 (可选)
```

*   **UI层 (UI Layer)**:
    *   **职责**: 构建和展示UI, 捕获用户输入。
    *   **内容**: Flutter Widgets, 通常是 `ConsumerWidget`。
    *   **规则**: UI层应保持“愚笨”, 只负责展示状态和发送用户事件。

*   **控制层 (Controller Layer)**:
    *   **职责**: 编排业务逻辑, 响应UI事件, 管理状态。
    *   **内容**: Riverpod 的 `Notifier` 类。
    *   **规则**: 业务逻辑的核心, 调用 `Service` 或其他 `Controller` 并更新 `Model`。

*   **模型层 (Model Layer)**:
    *   **职责**: 定义该功能的核心数据结构和状态。
    *   **内容**: 不可变的 (immutable) 模型类。
    *   **规则**: 纯数据容器, 不包含业务逻辑。

*   **服务层 (Service Layer)**:
    *   **职责**: 封装与外部世界的交互细节。
    *   **内容**: 封装第三方库 (如 `sherpa_onnx`)、API请求或本地数据库的类。
    *   **规则**: 为 `Controller` 提供简单的接口, 隐藏底层实现的复杂性。

## 3. 导航策略 (Navigation Strategy)

为了实现导航的中心化管理和解耦, 项目统一使用 `go_router` 包作为官方导航方案。

*   **核心优势**:
    *   **解耦**: 将路由定义与UI代码完全分离, UI组件只需请求路径, 无需关心页面如何构建。
    *   **中心化**: 所有路由路径和对应的页面都在一个地方集中管理。
    *   **强大功能**: 完美支持Web和移动应用的深链接 (Deep Linking)。

*   **架构集成**:
    1.  **位置**: 路由配置属于核心框架的一部分, 应放在 `lib/src/core/router/app_router.dart`。
    2.  **定义**: 在该文件中配置 `GoRouter` 实例, 定义所有页面路径和其对应的UI组件 (`Screen`)。
        ```dart
        // /lib/src/core/router/app_router.dart
        import 'package:go_router/go_router.dart';
        // Import your screens from the 'features' layer

        final GoRouter router = GoRouter(
          routes: [
            GoRoute(
              path: '/',
              // builder: (context, state) => AsrScreen(),
            ),
            GoRoute(
              path: '/settings',
              // builder: (context, state) => SettingsScreen(),
            ),
          ],
        );
        ```
    3.  **使用**: 在UI层中, 通过 `context.go()` 方法进行页面跳转, 传入目标路径。
        ```dart
        // 在某个UI组件中
        onPressed: () => context.go('/settings'),
        ```

## 4. 如何添加新功能

当需要添加一个新功能时 (例如, “用户设置”):

1.  在 `lib/src/features/` 下创建一个新的目录, 例如 `settings`。
2.  在 `settings/` 内部创建 `ui`, `controller`, `model`, `service` 等目录。
3.  在各自的目录中创建对应的文件, 如 `settings_screen.dart`, `settings_controller.dart`, `settings_model.dart`, `settings_service.dart`。
4.  如果发现某个组件 (例如一个自定义开关) 将来可能在其他地方也会用到, 那么从一开始就应该将它放在 `lib/src/shared/ui/` 中。
5.  最后, 将 `SettingsScreen` 集成到应用的导航流程中 (通过在 `app_router.dart` 中添加新的 `GoRoute`)。