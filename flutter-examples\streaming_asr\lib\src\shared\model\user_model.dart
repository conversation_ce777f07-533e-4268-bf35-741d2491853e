// Copyright (c) 2024 VocalMind AI

/// 用户信息模型
class User {
  final String id;
  final String email;
  final String? name;
  final String? avatar;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final UserStatus status;
  final UserRole role;
  final Map<String, dynamic>? metadata;

  const User({
    required this.id,
    required this.email,
    this.name,
    this.avatar,
    required this.createdAt,
    this.lastLoginAt,
    this.status = UserStatus.active,
    this.role = UserRole.user,
    this.metadata,
  });

  /// 从JSON创建用户对象
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String?,
      avatar: json['avatar'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastLoginAt: json['last_login_at'] != null
          ? DateTime.parse(json['last_login_at'] as String)
          : null,
      status: UserStatus.fromString(json['status'] as String? ?? 'active'),
      role: UserRole.fromString(json['role'] as String? ?? 'user'),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'avatar': avatar,
      'created_at': createdAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'status': status.value,
      'role': role.value,
      'metadata': metadata,
    };
  }

  /// 复制并修改部分属性
  User copyWith({
    String? id,
    String? email,
    String? name,
    String? avatar,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    UserStatus? status,
    UserRole? role,
    Map<String, dynamic>? metadata,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      status: status ?? this.status,
      role: role ?? this.role,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 获取显示名称
  String get displayName => name ?? email.split('@').first;

  /// 检查是否有头像
  bool get hasAvatar => avatar != null && avatar!.isNotEmpty;

  /// 检查是否是管理员
  bool get isAdmin => role == UserRole.admin;

  /// 检查是否是活跃用户
  bool get isActive => status == UserStatus.active;

  @override
  String toString() {
    return 'User(id: $id, email: $email, name: $name, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 用户状态枚举
enum UserStatus {
  active('active'),
  inactive('inactive'),
  suspended('suspended'),
  deleted('deleted');

  const UserStatus(this.value);

  final String value;

  static UserStatus fromString(String value) {
    return UserStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => UserStatus.active,
    );
  }

  @override
  String toString() => value;
}

/// 用户角色枚举
enum UserRole {
  user('user'),
  admin('admin'),
  moderator('moderator');

  const UserRole(this.value);

  final String value;

  static UserRole fromString(String value) {
    return UserRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => UserRole.user,
    );
  }

  @override
  String toString() => value;
}

/// 用户认证信息模型
class AuthInfo {
  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;
  final String tokenType;

  const AuthInfo({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
    this.tokenType = 'Bearer',
  });

  /// 从JSON创建认证信息
  factory AuthInfo.fromJson(Map<String, dynamic> json) {
    return AuthInfo(
      accessToken: json['access_token'] as String,
      refreshToken: json['refresh_token'] as String,
      expiresAt: DateTime.fromMillisecondsSinceEpoch(
        (json['expires_at'] as int) * 1000,
      ),
      tokenType: json['token_type'] as String? ?? 'Bearer',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'expires_at': expiresAt.millisecondsSinceEpoch ~/ 1000,
      'token_type': tokenType,
    };
  }

  /// 检查Token是否过期
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// 检查Token是否即将过期（5分钟内）
  bool get isExpiringSoon {
    final fiveMinutesFromNow = DateTime.now().add(const Duration(minutes: 5));
    return fiveMinutesFromNow.isAfter(expiresAt);
  }

  /// 获取Authorization头部值
  String get authorizationHeader => '$tokenType $accessToken';

  @override
  String toString() {
    return 'AuthInfo(tokenType: $tokenType, expiresAt: $expiresAt)';
  }
}

/// 用户偏好设置模型
class UserPreferences {
  final String userId;
  final String language;
  final String theme;
  final bool enableNotifications;
  final bool enableSounds;
  final Map<String, dynamic> customSettings;

  const UserPreferences({
    required this.userId,
    this.language = 'zh-CN',
    this.theme = 'system',
    this.enableNotifications = true,
    this.enableSounds = true,
    this.customSettings = const {},
  });

  /// 从JSON创建用户偏好
  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      userId: json['user_id'] as String,
      language: json['language'] as String? ?? 'zh-CN',
      theme: json['theme'] as String? ?? 'system',
      enableNotifications: json['enable_notifications'] as bool? ?? true,
      enableSounds: json['enable_sounds'] as bool? ?? true,
      customSettings: json['custom_settings'] as Map<String, dynamic>? ?? {},
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'language': language,
      'theme': theme,
      'enable_notifications': enableNotifications,
      'enable_sounds': enableSounds,
      'custom_settings': customSettings,
    };
  }

  /// 复制并修改部分属性
  UserPreferences copyWith({
    String? userId,
    String? language,
    String? theme,
    bool? enableNotifications,
    bool? enableSounds,
    Map<String, dynamic>? customSettings,
  }) {
    return UserPreferences(
      userId: userId ?? this.userId,
      language: language ?? this.language,
      theme: theme ?? this.theme,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableSounds: enableSounds ?? this.enableSounds,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  @override
  String toString() {
    return 'UserPreferences(userId: $userId, language: $language, theme: $theme)';
  }
}
