// Copyright (c) 2024 VocalMind AI
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/router/app_router.dart';
import '../../../shared/ui/custom_button.dart';

/// 欢迎页面
/// 
/// 应用启动时的欢迎界面，提供功能介绍和认证入口
class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<WelcomePageData> _pages = [
    WelcomePageData(
      icon: Icons.mic,
      title: '🎙️ 智能语音识别',
      description: '先进的语音识别技术，支持多种语言和方言，准确率高达99%',
      color: Colors.blue,
    ),
    WelcomePageData(
      icon: Icons.smart_toy,
      title: '🤖 AI智能助手',
      description: '集成多种AI模型，自动优化识别结果，提供智能问答和内容分析',
      color: Colors.green,
    ),
    WelcomePageData(
      icon: Icons.security,
      title: '🔒 隐私保护',
      description: '支持本地处理和云端处理两种模式，您的数据安全由您掌控',
      color: Colors.orange,
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // 顶部Logo和标题
            _buildHeader(theme),
            
            // 功能介绍页面
            Expanded(
              child: _buildPageView(),
            ),
            
            // 页面指示器
            _buildPageIndicator(theme),
            
            // 底部按钮
            _buildBottomButtons(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Logo
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.record_voice_over,
              size: 40,
              color: theme.colorScheme.onPrimary,
            ),
          ),
          const SizedBox(height: 16),
          
          // 应用名称
          Text(
            'VocalMind AI',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          
          // 副标题
          Text(
            '智能语音识别与AI助手',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageView() {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: (index) {
        setState(() {
          _currentPage = index;
        });
      },
      itemCount: _pages.length,
      itemBuilder: (context, index) {
        return _buildWelcomePage(_pages[index]);
      },
    );
  }

  Widget _buildWelcomePage(WelcomePageData pageData) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 图标
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: pageData.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              pageData.icon,
              size: 60,
              color: pageData.color,
            ),
          ),
          const SizedBox(height: 32),
          
          // 标题
          Text(
            pageData.title,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          
          // 描述
          Text(
            pageData.description,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicator(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          _pages.length,
          (index) => Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            width: _currentPage == index ? 24 : 8,
            height: 8,
            decoration: BoxDecoration(
              color: _currentPage == index
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomButtons(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // 登录按钮
          CustomButton(
            text: '登录',
            onPressed: () => context.go(AppRoutes.login),
            style: CustomButtonStyle.primary,
            size: CustomButtonSize.large,
          ),
          const SizedBox(height: 12),
          
          // 注册按钮
          CustomButton(
            text: '注册账号',
            onPressed: () => context.go(AppRoutes.register),
            style: CustomButtonStyle.outline,
            size: CustomButtonSize.large,
          ),
          const SizedBox(height: 16),
          
          // 跳过按钮
          TextButton(
            onPressed: () => context.go(AppRoutes.home),
            child: Text(
              '跳过，直接体验',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 欢迎页面数据模型
class WelcomePageData {
  final IconData icon;
  final String title;
  final String description;
  final Color color;

  const WelcomePageData({
    required this.icon,
    required this.title,
    required this.description,
    required this.color,
  });
}
