// Copyright (c) 2024 VocalMind AI
import 'package:intl/intl.dart';

/// 日期时间工具类
class DateUtils {
  static const String defaultDateFormat = 'yyyy-MM-dd';
  static const String defaultTimeFormat = 'HH:mm:ss';
  static const String defaultDateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String chineseDateTimeFormat = 'yyyy年MM月dd日 HH:mm';
  static const String shortDateTimeFormat = 'MM-dd HH:mm';
  
  /// 格式化日期时间
  static String formatDateTime(
    DateTime dateTime, {
    String format = defaultDateTimeFormat,
  }) {
    return DateFormat(format).format(dateTime);
  }
  
  /// 格式化日期
  static String formatDate(
    DateTime dateTime, {
    String format = defaultDateFormat,
  }) {
    return DateFormat(format).format(dateTime);
  }
  
  /// 格式化时间
  static String formatTime(
    DateTime dateTime, {
    String format = defaultTimeFormat,
  }) {
    return DateFormat(format).format(dateTime);
  }
  
  /// 格式化为中文日期时间
  static String formatChineseDateTime(DateTime dateTime) {
    return DateFormat(chineseDateTimeFormat, 'zh_CN').format(dateTime);
  }
  
  /// 格式化为相对时间（如：刚刚、5分钟前、昨天等）
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inSeconds < 60) {
      return '刚刚';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays == 1) {
      return '昨天 ${formatTime(dateTime, format: 'HH:mm')}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '${weeks}周前';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '${months}个月前';
    } else {
      final years = (difference.inDays / 365).floor();
      return '${years}年前';
    }
  }
  
  /// 格式化时长（秒数转换为时:分:秒格式）
  static String formatDuration(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
             '${minutes.toString().padLeft(2, '0')}:'
             '${remainingSeconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
             '${remainingSeconds.toString().padLeft(2, '0')}';
    }
  }
  
  /// 解析日期时间字符串
  static DateTime? parseDateTime(String dateTimeString, {String? format}) {
    try {
      if (format != null) {
        return DateFormat(format).parse(dateTimeString);
      } else {
        return DateTime.parse(dateTimeString);
      }
    } catch (e) {
      return null;
    }
  }
  
  /// 获取今天的开始时间（00:00:00）
  static DateTime getTodayStart() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }
  
  /// 获取今天的结束时间（23:59:59）
  static DateTime getTodayEnd() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, 23, 59, 59);
  }
  
  /// 获取本周的开始时间（周一00:00:00）
  static DateTime getWeekStart() {
    final now = DateTime.now();
    final weekday = now.weekday;
    final daysToSubtract = weekday - 1;
    final weekStart = now.subtract(Duration(days: daysToSubtract));
    return DateTime(weekStart.year, weekStart.month, weekStart.day);
  }
  
  /// 获取本月的开始时间
  static DateTime getMonthStart() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, 1);
  }
  
  /// 检查是否是今天
  static bool isToday(DateTime dateTime) {
    final now = DateTime.now();
    return dateTime.year == now.year &&
           dateTime.month == now.month &&
           dateTime.day == now.day;
  }
  
  /// 检查是否是昨天
  static bool isYesterday(DateTime dateTime) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return dateTime.year == yesterday.year &&
           dateTime.month == yesterday.month &&
           dateTime.day == yesterday.day;
  }
  
  /// 检查是否是本周
  static bool isThisWeek(DateTime dateTime) {
    final weekStart = getWeekStart();
    final weekEnd = weekStart.add(const Duration(days: 7));
    return dateTime.isAfter(weekStart) && dateTime.isBefore(weekEnd);
  }
  
  /// 检查是否是本月
  static bool isThisMonth(DateTime dateTime) {
    final now = DateTime.now();
    return dateTime.year == now.year && dateTime.month == now.month;
  }
  
  /// 获取两个日期之间的天数差
  static int daysBetween(DateTime start, DateTime end) {
    final startDate = DateTime(start.year, start.month, start.day);
    final endDate = DateTime(end.year, end.month, end.day);
    return endDate.difference(startDate).inDays;
  }
  
  /// 添加工作日（跳过周末）
  static DateTime addWorkdays(DateTime date, int workdays) {
    var result = date;
    var remainingDays = workdays;
    
    while (remainingDays > 0) {
      result = result.add(const Duration(days: 1));
      // 跳过周末（周六=6，周日=7）
      if (result.weekday < 6) {
        remainingDays--;
      }
    }
    
    return result;
  }
  
  /// 获取月份的天数
  static int getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }
  
  /// 检查是否是闰年
  static bool isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }
}
