// Copyright (c)  2024  Xiaomi Corporation
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../model/asr_state.dart';
import '../controller/asr_controller.dart';

/// ASR屏幕
/// 
/// 使用ConsumerStatefulWidget，只负责展示状态和发送用户事件
class AsrScreen extends ConsumerStatefulWidget {
  const AsrScreen({super.key});

  @override
  ConsumerState<AsrScreen> createState() => _AsrScreenState();
}

class _AsrScreenState extends ConsumerState<AsrScreen> {
  late final TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 监听ASR状态变化
    final asrState = ref.watch(asrControllerProvider);
    
    // 更新文本框内容
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final displayText = asrState.displayText;
      if (_controller.text != displayText) {
        _controller.value = TextEditingValue(
          text: displayText,
          selection: TextSelection.collapsed(offset: displayText.length),
        );
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Real-time speech recognition'),
        actions: [
          if (asrState.status == AsrStatus.error)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                ref.read(asrControllerProvider.notifier).clearError();
              },
              tooltip: 'Clear error',
            ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: asrState.finalizedResults.isNotEmpty ||
                    asrState.currentResult.text.isNotEmpty
                ? () {
                    ref.read(asrControllerProvider.notifier).clearResults();
                  }
                : null,
            tooltip: 'Clear results',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 状态指示器
            _buildStatusIndicator(asrState),
            const SizedBox(height: 20),
            
            // 文本显示区域
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: TextField(
                  controller: _controller,
                  maxLines: null,
                  expands: true,
                  readOnly: true,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    hintText: 'Recognition results will appear here...',
                  ),
                  style: const TextStyle(fontSize: 16.0),
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 控制按钮区域
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildRecordButton(asrState),
                const SizedBox(width: 20),
                _buildStatusText(asrState),
              ],
            ),
            
            // 错误信息显示
            if (asrState.errorMessage != null) ...[
              const SizedBox(height: 20),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  border: Border.all(color: Colors.red.shade200),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error, color: Colors.red.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        asrState.errorMessage!,
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontSize: 14.0,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建状态指示器
  Widget _buildStatusIndicator(AsrState asrState) {
    Color color;
    IconData icon;
    String text;

    switch (asrState.status) {
      case AsrStatus.uninitialized:
        color = Colors.grey;
        icon = Icons.hourglass_empty;
        text = 'Initializing...';
        break;
      case AsrStatus.ready:
        color = Colors.green;
        icon = Icons.check_circle;
        text = 'Ready';
        break;
      case AsrStatus.recording:
        color = Colors.red;
        icon = Icons.mic;
        text = 'Recording...';
        break;
      case AsrStatus.stopped:
        color = Colors.orange;
        icon = Icons.stop;
        text = 'Stopped';
        break;
      case AsrStatus.error:
        color = Colors.red;
        icon = Icons.error;
        text = 'Error';
        break;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 8),
        Text(
          text,
          style: TextStyle(
            color: color,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建录音按钮
  Widget _buildRecordButton(AsrState asrState) {
    final isRecording = asrState.status == AsrStatus.recording;
    final isDisabled = asrState.status == AsrStatus.error;
    
    return ClipOval(
      child: Material(
        color: isRecording
            ? Colors.red.withValues(alpha: 0.1)
            : Theme.of(context).primaryColor.withValues(alpha: 0.1),
        child: InkWell(
          onTap: isDisabled
              ? null
              : () {
                  if (isRecording) {
                    ref.read(asrControllerProvider.notifier).stopRecording();
                  } else {
                    ref.read(asrControllerProvider.notifier).startRecording();
                  }
                },
          child: SizedBox(
            width: 80,
            height: 80,
            child: Icon(
              isRecording ? Icons.stop : Icons.mic,
              color: isDisabled
                  ? Colors.grey
                  : isRecording
                      ? Colors.red
                      : Theme.of(context).primaryColor,
              size: 40,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建状态文本
  Widget _buildStatusText(AsrState asrState) {
    final isRecording = asrState.status == AsrStatus.recording;
    return Text(
      isRecording ? 'Stop' : 'Start',
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w500,
      ),
    );
  }
}
