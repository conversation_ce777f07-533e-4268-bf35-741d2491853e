# VocalMind AI Flutter Migration TODO List

## 阶段1: 基础架构搭建 (1-2周)

### 1.1 Core层基础设施完善
- [x] **API客户端封装** (`core/api/`)
  - [x] 创建基础HTTP客户端 (使用dio)
  - [x] 实现请求/响应拦截器
  - [x] 错误处理机制
  - [x] API响应模型基类
  - [x] 网络状态监控

- [x] **基础控制器类** (`core/base/`)
  - [x] BaseController抽象类
  - [x] BaseNotifier (Riverpod)
  - [x] 生命周期管理
  - [x] 错误状态处理
  - [x] 加载状态管理

- [x] **日志服务** (`core/log/`)
  - [x] 日志级别配置
  - [x] 文件日志输出
  - [x] 网络日志上传
  - [x] 调试模式开关

- [x] **路由系统** (`core/router/`)
  - [x] GoRouter配置
  - [x] 路由守卫 (认证检查)
  - [x] 深链接支持
  - [x] 路由动画配置

- [x] **工具函数** (`core/utils/`)
  - [x] 时间格式化工具
  - [x] 文件操作工具
  - [x] 加密解密工具
  - [x] 设备信息获取

### 1.2 Shared层共享组件
- [x] **共享UI组件** (`shared/ui/`)
  - [x] 自定义按钮组件
  - [x] 加载指示器
  - [x] 错误提示组件
  - [x] 对话框组件
  - [x] 底部导航栏

- [x] **共享模型** (`shared/model/`)
  - [x] 用户信息模型
  - [x] API响应基础模型
  - [x] 错误信息模型
  - [x] 分页数据模型

- [x] **共享服务** (`shared/service/`)
  - [x] 本地存储服务
  - [x] 权限管理服务
  - [x] 设备信息服务
  - [x] 文件管理服务

### 1.3 依赖包集成
- [x] **添加必要依赖**
  - [x] dio (网络请求)
  - [x] sqflite (本地数据库)
  - [x] shared_preferences (简单存储)
  - [x] flutter_local_notifications (通知)
  - [x] file_picker (文件选择)
  - [x] permission_handler (权限管理)
  - [x] flutter_background_service (后台服务)

## 阶段2: 核心功能迁移 (3-4周)

### 2.1 用户认证模块 (`features/auth/`)
- [x] **认证UI页面**
  - [x] WelcomeScreen (欢迎页面)
  - [x] LoginScreen (登录页面)
  - [x] RegisterScreen (注册页面)
  - [ ] ForgotPasswordScreen (忘记密码)

- [x] **认证控制器**
  - [x] AuthController (认证状态管理)
  - [x] 登录/注册逻辑
  - [x] 认证状态持久化
  - [x] 自动登录功能

- [x] **认证服务**
  - [x] AuthService (API调用)
  - [x] Token管理
  - [x] 用户信息缓存
  - [x] 登出清理

- [x] **认证模型**
  - [x] User模型
  - [x] AuthState模型
  - [x] LoginRequest/Response模型

### 2.2 语音识别模块 (`features/asr/`)
- [x] **ASR UI界面**
  - [x] EnhancedAsrScreen (主录音界面)
  - [x] 录音控制组件
  - [x] 转录结果显示
  - [x] 状态指示器

- [x] **ASR控制器**
  - [x] EnhancedAsrController (录音状态管理)
  - [x] 录音开始/停止逻辑
  - [x] 实时转录结果处理
  - [x] 基于BaseController的统一状态管理

- [x] **ASR服务**
  - [x] 集成现有AsrService和AudioService
  - [x] 音频流处理
  - [x] 错误处理和日志记录

- [x] **ASR模型**
  - [x] AsrState模型
  - [x] AsrResult模型
  - [x] AsrConfig模型

### 2.3 会议记录管理模块 (`features/meeting/`)
- [x] **会议记录UI**
  - [x] MeetingListScreen (记录列表)
  - [x] MeetingDetailScreen (记录详情)
  - [x] 搜索和过滤功能

- [x] **会议记录控制器**
  - [x] MeetingController (记录管理)
  - [x] 记录CRUD操作
  - [x] 搜索和筛选
  - [x] 统计信息

- [x] **会议记录服务**
  - [x] MeetingDatabaseService (本地存储)
  - [x] SQLite数据库操作
  - [x] 标签管理

- [x] **会议记录模型**
  - [x] MeetingRecord模型
  - [x] MeetingFilter模型
  - [x] MeetingStatistics模型

## 阶段3: 高级功能实现 (2-3周)

### 3.1 AI交互模块 (`features/ai_chat/`)
- [ ] **AI聊天UI**
  - [ ] AiChatScreen (聊天界面)
  - [ ] ChatMessageWidget (消息组件)
  - [ ] ChatInputWidget (输入组件)

- [ ] **AI聊天控制器**
  - [ ] AiChatController (聊天管理)
  - [ ] 消息发送/接收
  - [ ] 聊天历史管理

- [ ] **AI聊天服务**
  - [ ] LLMService (大语言模型API)
  - [ ] 多模型支持
  - [ ] 聊天历史存储

- [ ] **AI聊天模型**
  - [ ] ChatMessage模型
  - [ ] ChatSession模型
  - [ ] LLMConfig模型

### 3.2 待办事项模块 (`features/todo/`)
- [ ] **待办事项UI**
  - [ ] TodoListScreen (待办列表)
  - [ ] TodoEditScreen (编辑页面)
  - [ ] TodoWidget (待办项组件)

- [ ] **待办事项控制器**
  - [ ] TodoController (待办管理)
  - [ ] 自动生成待办
  - [ ] 提醒功能

- [ ] **待办事项服务**
  - [ ] TodoStorageService (本地存储)
  - [ ] NotificationService (通知服务)
  - [ ] ReminderService (提醒服务)

- [ ] **待办事项模型**
  - [ ] TodoItem模型
  - [ ] TodoCategory模型
  - [ ] ReminderConfig模型

### 3.3 设置模块 (`features/settings/`)
- [ ] **设置UI**
  - [ ] SettingsScreen (主设置页面)
  - [ ] ServerSettingsScreen (服务器设置)
  - [ ] LLMSettingsScreen (AI模型设置)

- [ ] **设置控制器**
  - [ ] SettingsController (设置管理)
  - [ ] 配置保存/加载
  - [ ] 设置验证

- [ ] **设置服务**
  - [ ] ConfigService (配置管理)
  - [ ] 设置同步服务

- [ ] **设置模型**
  - [ ] AppSettings模型
  - [ ] ServerConfig模型
  - [ ] LLMConfig模型

## 阶段4: 优化和完善 (1-2周)

### 4.1 性能优化
- [ ] **内存优化**
  - [ ] 音频数据流优化
  - [ ] 大文件处理优化
  - [ ] Widget重建优化

- [ ] **网络优化**
  - [ ] 请求缓存机制
  - [ ] 离线模式支持
  - [ ] 网络重试机制

- [ ] **存储优化**
  - [ ] 数据库索引优化
  - [ ] 文件压缩存储
  - [ ] 缓存清理机制

### 4.2 UI/UX改进
- [ ] **界面优化**
  - [ ] 响应式布局适配
  - [ ] 暗色主题支持
  - [ ] 动画效果优化

- [ ] **交互优化**
  - [ ] 手势操作支持
  - [ ] 快捷键支持
  - [ ] 无障碍功能

### 4.3 测试和调试
- [ ] **单元测试**
  - [ ] Controller测试
  - [ ] Service测试
  - [ ] Model测试

- [ ] **Widget测试**
  - [ ] UI组件测试
  - [ ] 交互测试
  - [ ] 状态测试

- [ ] **集成测试**
  - [ ] 端到端流程测试
  - [ ] 性能测试
  - [ ] 兼容性测试

### 4.4 文档完善
- [ ] **技术文档**
  - [ ] API文档
  - [ ] 架构文档
  - [ ] 部署文档

- [ ] **用户文档**
  - [ ] 使用说明
  - [ ] 常见问题
  - [ ] 更新日志

## 关键里程碑

### 里程碑1: 基础架构完成 (第2周末)
- [ ] Core层基础设施完善
- [ ] Shared层共享组件就绪
- [ ] 路由和状态管理配置完成

### 里程碑2: 核心功能可用 (第6周末)
- [ ] 用户认证流程完整
- [ ] 语音识别基本功能可用
- [ ] 会议记录管理基础功能

### 里程碑3: 功能完整性 (第9周末)
- [ ] 所有主要功能模块完成
- [ ] AI交互和待办事项功能可用
- [ ] 设置功能完整

### 里程碑4: 产品就绪 (第11周末)
- [ ] 性能优化完成
- [ ] 测试覆盖率达标
- [ ] 文档完善
- [ ] 可发布版本就绪

## 风险缓解措施

### 技术风险
- [ ] sherpa-onnx集成问题 → 提前验证和测试
- [ ] 后台录音权限问题 → 研究最佳实践
- [ ] 内存管理问题 → 性能监控和优化

### 进度风险
- [ ] 功能复杂度超预期 → 分阶段交付
- [ ] 技术难点阻塞 → 并行开发和备选方案
- [ ] 测试时间不足 → 开发过程中持续测试

### 质量风险
- [ ] 用户体验下降 → 原型验证和用户反馈
- [ ] 性能不达标 → 性能基准测试
- [ ] 稳定性问题 → 充分的集成测试

## 当前优先级 (本次对话)

### 已完成 ✅
1. [x] **完善Core层API客户端** - API客户端、日志服务、基础控制器、路由系统
2. [x] **建立用户认证模块** - 完整的认证流程和UI
3. [x] **优化ASR模块** - 增强的语音识别界面和控制器
4. [x] **会议记录管理** - 数据库设计、CRUD操作、列表界面
5. [x] **共享UI组件库** - 自定义按钮、加载指示器等

### 下一步计划 (中优先级)
1. [x] **会议详情页面** - MeetingDetailScreen
2. [x] **AI交互功能** - AiChatScreen和相关服务
3. [ ] **待办事项管理** - TodoListScreen和TodoEditScreen

### 后续规划 (低优先级)
1. [ ] **设置功能完善** - SettingsScreen和配置管理
2. [ ] **高级功能** - 导出、分享、后台服务等
3. [ ] **性能优化和测试** - 最终优化和完善
