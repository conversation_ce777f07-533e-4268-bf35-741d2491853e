// Copyright (c) 2024 VocalMind AI
import 'package:flutter/material.dart';

/// 自定义按钮样式枚举
enum CustomButtonStyle {
  primary,
  secondary,
  outline,
  text,
  danger,
}

/// 自定义按钮大小枚举
enum CustomButtonSize {
  small,
  medium,
  large,
}

/// 自定义按钮组件
/// 
/// 提供统一的按钮样式和交互
class CustomButton extends StatelessWidget {
  const CustomButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.style = CustomButtonStyle.primary,
    this.size = CustomButtonSize.medium,
    this.isLoading = false,
    this.isEnabled = true,
    this.icon,
    this.width,
    this.height,
  });

  final String text;
  final VoidCallback? onPressed;
  final CustomButtonStyle style;
  final CustomButtonSize size;
  final bool isLoading;
  final bool isEnabled;
  final IconData? icon;
  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return SizedBox(
      width: width ?? _getButtonWidth(),
      height: height ?? _getButtonHeight(),
      child: _buildButton(context, colorScheme),
    );
  }

  Widget _buildButton(BuildContext context, ColorScheme colorScheme) {
    final isDisabled = !isEnabled || isLoading;
    
    switch (style) {
      case CustomButtonStyle.primary:
        return ElevatedButton(
          onPressed: isDisabled ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: colorScheme.primary,
            foregroundColor: colorScheme.onPrimary,
            disabledBackgroundColor: colorScheme.outline,
            disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius()),
            ),
            padding: _getPadding(),
          ),
          child: _buildButtonContent(colorScheme.onPrimary),
        );
        
      case CustomButtonStyle.secondary:
        return ElevatedButton(
          onPressed: isDisabled ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: colorScheme.secondary,
            foregroundColor: colorScheme.onSecondary,
            disabledBackgroundColor: colorScheme.outline,
            disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius()),
            ),
            padding: _getPadding(),
          ),
          child: _buildButtonContent(colorScheme.onSecondary),
        );
        
      case CustomButtonStyle.outline:
        return OutlinedButton(
          onPressed: isDisabled ? null : onPressed,
          style: OutlinedButton.styleFrom(
            foregroundColor: colorScheme.primary,
            disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
            side: BorderSide(
              color: isDisabled ? colorScheme.outline : colorScheme.primary,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius()),
            ),
            padding: _getPadding(),
          ),
          child: _buildButtonContent(colorScheme.primary),
        );
        
      case CustomButtonStyle.text:
        return TextButton(
          onPressed: isDisabled ? null : onPressed,
          style: TextButton.styleFrom(
            foregroundColor: colorScheme.primary,
            disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius()),
            ),
            padding: _getPadding(),
          ),
          child: _buildButtonContent(colorScheme.primary),
        );
        
      case CustomButtonStyle.danger:
        return ElevatedButton(
          onPressed: isDisabled ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: colorScheme.error,
            foregroundColor: colorScheme.onError,
            disabledBackgroundColor: colorScheme.outline,
            disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius()),
            ),
            padding: _getPadding(),
          ),
          child: _buildButtonContent(colorScheme.onError),
        );
    }
  }

  Widget _buildButtonContent(Color textColor) {
    if (isLoading) {
      return SizedBox(
        width: _getLoadingSize(),
        height: _getLoadingSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: _getIconSize()),
          SizedBox(width: _getIconSpacing()),
          Text(
            text,
            style: TextStyle(
              fontSize: _getFontSize(),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    }

    return Text(
      text,
      style: TextStyle(
        fontSize: _getFontSize(),
        fontWeight: FontWeight.w500,
      ),
    );
  }

  double? _getButtonWidth() {
    switch (size) {
      case CustomButtonSize.small:
        return null; // 自适应宽度
      case CustomButtonSize.medium:
        return null; // 自适应宽度
      case CustomButtonSize.large:
        return double.infinity; // 全宽
    }
  }

  double _getButtonHeight() {
    switch (size) {
      case CustomButtonSize.small:
        return 32;
      case CustomButtonSize.medium:
        return 40;
      case CustomButtonSize.large:
        return 48;
    }
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case CustomButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
      case CustomButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case CustomButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 12);
    }
  }

  double _getBorderRadius() {
    switch (size) {
      case CustomButtonSize.small:
        return 6;
      case CustomButtonSize.medium:
        return 8;
      case CustomButtonSize.large:
        return 10;
    }
  }

  double _getFontSize() {
    switch (size) {
      case CustomButtonSize.small:
        return 12;
      case CustomButtonSize.medium:
        return 14;
      case CustomButtonSize.large:
        return 16;
    }
  }

  double _getIconSize() {
    switch (size) {
      case CustomButtonSize.small:
        return 16;
      case CustomButtonSize.medium:
        return 18;
      case CustomButtonSize.large:
        return 20;
    }
  }

  double _getIconSpacing() {
    switch (size) {
      case CustomButtonSize.small:
        return 4;
      case CustomButtonSize.medium:
        return 6;
      case CustomButtonSize.large:
        return 8;
    }
  }

  double _getLoadingSize() {
    switch (size) {
      case CustomButtonSize.small:
        return 16;
      case CustomButtonSize.medium:
        return 18;
      case CustomButtonSize.large:
        return 20;
    }
  }
}

/// 浮动操作按钮组件
class CustomFloatingActionButton extends StatelessWidget {
  const CustomFloatingActionButton({
    super.key,
    required this.onPressed,
    required this.icon,
    this.isLoading = false,
    this.backgroundColor,
    this.foregroundColor,
    this.tooltip,
  });

  final VoidCallback? onPressed;
  final IconData icon;
  final bool isLoading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final String? tooltip;

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: isLoading ? null : onPressed,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      tooltip: tooltip,
      child: isLoading
          ? const SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Icon(icon),
    );
  }
}
