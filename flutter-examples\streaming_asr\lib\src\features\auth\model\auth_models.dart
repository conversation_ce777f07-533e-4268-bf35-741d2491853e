// Copyright (c) 2024 VocalMind AI

/// 登录请求模型
class LoginRequest {
  final String email;
  final String password;
  final bool rememberMe;

  const LoginRequest({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'remember_me': rememberMe,
    };
  }

  @override
  String toString() {
    return 'LoginRequest(email: $email, rememberMe: $rememberMe)';
  }
}

/// 注册请求模型
class RegisterRequest {
  final String email;
  final String password;
  final String confirmPassword;
  final String? name;
  final bool agreeToTerms;

  const RegisterRequest({
    required this.email,
    required this.password,
    required this.confirmPassword,
    this.name,
    this.agreeToTerms = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'confirm_password': confirmPassword,
      'name': name,
      'agree_to_terms': agreeToTerms,
    };
  }

  /// 验证密码是否匹配
  bool get passwordsMatch => password == confirmPassword;

  /// 验证是否同意条款
  bool get isValid => agreeToTerms && passwordsMatch;

  @override
  String toString() {
    return 'RegisterRequest(email: $email, name: $name, agreeToTerms: $agreeToTerms)';
  }
}

/// 忘记密码请求模型
class ForgotPasswordRequest {
  final String email;

  const ForgotPasswordRequest({
    required this.email,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }

  @override
  String toString() {
    return 'ForgotPasswordRequest(email: $email)';
  }
}

/// 重置密码请求模型
class ResetPasswordRequest {
  final String token;
  final String newPassword;
  final String confirmPassword;

  const ResetPasswordRequest({
    required this.token,
    required this.newPassword,
    required this.confirmPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'new_password': newPassword,
      'confirm_password': confirmPassword,
    };
  }

  /// 验证密码是否匹配
  bool get passwordsMatch => newPassword == confirmPassword;

  @override
  String toString() {
    return 'ResetPasswordRequest(token: ${token.substring(0, 8)}...)';
  }
}

/// 认证响应模型
class AuthResponse {
  final String accessToken;
  final String refreshToken;
  final int expiresIn;
  final String tokenType;
  final Map<String, dynamic> user;

  const AuthResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresIn,
    required this.tokenType,
    required this.user,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      accessToken: json['access_token'] as String,
      refreshToken: json['refresh_token'] as String,
      expiresIn: json['expires_in'] as int,
      tokenType: json['token_type'] as String? ?? 'Bearer',
      user: json['user'] as Map<String, dynamic>,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'expires_in': expiresIn,
      'token_type': tokenType,
      'user': user,
    };
  }

  /// 获取过期时间
  DateTime get expiresAt {
    return DateTime.now().add(Duration(seconds: expiresIn));
  }

  @override
  String toString() {
    return 'AuthResponse(tokenType: $tokenType, expiresIn: $expiresIn)';
  }
}

/// 认证状态枚举
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// 认证状态模型
class AuthState {
  final AuthStatus status;
  final String? accessToken;
  final String? refreshToken;
  final DateTime? expiresAt;
  final Map<String, dynamic>? user;
  final String? error;

  const AuthState({
    required this.status,
    this.accessToken,
    this.refreshToken,
    this.expiresAt,
    this.user,
    this.error,
  });

  /// 创建初始状态
  factory AuthState.initial() {
    return const AuthState(status: AuthStatus.initial);
  }

  /// 创建加载状态
  factory AuthState.loading() {
    return const AuthState(status: AuthStatus.loading);
  }

  /// 创建已认证状态
  factory AuthState.authenticated({
    required String accessToken,
    required String refreshToken,
    required DateTime expiresAt,
    required Map<String, dynamic> user,
  }) {
    return AuthState(
      status: AuthStatus.authenticated,
      accessToken: accessToken,
      refreshToken: refreshToken,
      expiresAt: expiresAt,
      user: user,
    );
  }

  /// 创建未认证状态
  factory AuthState.unauthenticated() {
    return const AuthState(status: AuthStatus.unauthenticated);
  }

  /// 创建错误状态
  factory AuthState.error(String error) {
    return AuthState(
      status: AuthStatus.error,
      error: error,
    );
  }

  /// 复制状态并修改部分属性
  AuthState copyWith({
    AuthStatus? status,
    String? accessToken,
    String? refreshToken,
    DateTime? expiresAt,
    Map<String, dynamic>? user,
    String? error,
  }) {
    return AuthState(
      status: status ?? this.status,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      expiresAt: expiresAt ?? this.expiresAt,
      user: user ?? this.user,
      error: error ?? this.error,
    );
  }

  /// 检查是否已认证
  bool get isAuthenticated => status == AuthStatus.authenticated;

  /// 检查是否正在加载
  bool get isLoading => status == AuthStatus.loading;

  /// 检查是否有错误
  bool get hasError => status == AuthStatus.error;

  /// 检查Token是否过期
  bool get isTokenExpired {
    if (expiresAt == null) return true;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// 检查Token是否即将过期（5分钟内）
  bool get isTokenExpiringSoon {
    if (expiresAt == null) return true;
    final fiveMinutesFromNow = DateTime.now().add(const Duration(minutes: 5));
    return fiveMinutesFromNow.isAfter(expiresAt!);
  }

  /// 获取Authorization头部值
  String? get authorizationHeader {
    if (accessToken == null) return null;
    return 'Bearer $accessToken';
  }

  @override
  String toString() {
    return 'AuthState(status: $status, hasToken: ${accessToken != null}, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthState &&
        other.status == status &&
        other.accessToken == accessToken &&
        other.refreshToken == refreshToken &&
        other.expiresAt == expiresAt &&
        other.user == user &&
        other.error == error;
  }

  @override
  int get hashCode {
    return Object.hash(
      status,
      accessToken,
      refreshToken,
      expiresAt,
      user,
      error,
    );
  }
}
