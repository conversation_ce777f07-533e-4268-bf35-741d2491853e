// Copyright (c) 2024 VocalMind AI
import 'package:uuid/uuid.dart';

/// 聊天消息模型
class ChatMessage {
  final String id;
  final String content;
  final bool isFromUser;
  final DateTime timestamp;
  final String? meetingRecordId;
  final ChatMessageStatus status;
  final Map<String, dynamic>? metadata;

  const ChatMessage({
    required this.id,
    required this.content,
    required this.isFromUser,
    required this.timestamp,
    this.meetingRecordId,
    this.status = ChatMessageStatus.sent,
    this.metadata,
  });

  /// 创建用户消息
  factory ChatMessage.user({
    required String content,
    String? meetingRecordId,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: const Uuid().v4(),
      content: content,
      isFromUser: true,
      timestamp: DateTime.now(),
      meetingRecordId: meetingRecordId,
      status: ChatMessageStatus.sent,
      metadata: metadata,
    );
  }

  /// 创建AI消息
  factory ChatMessage.ai({
    required String content,
    String? meetingRecordId,
    ChatMessageStatus status = ChatMessageStatus.sent,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: const Uuid().v4(),
      content: content,
      isFromUser: false,
      timestamp: DateTime.now(),
      meetingRecordId: meetingRecordId,
      status: status,
      metadata: metadata,
    );
  }

  /// 从JSON创建消息
  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] as String,
      content: json['content'] as String,
      isFromUser: json['is_from_user'] as bool,
      timestamp: DateTime.parse(json['timestamp'] as String),
      meetingRecordId: json['meeting_record_id'] as String?,
      status: ChatMessageStatus.fromString(json['status'] as String? ?? 'sent'),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'is_from_user': isFromUser,
      'timestamp': timestamp.toIso8601String(),
      'meeting_record_id': meetingRecordId,
      'status': status.value,
      'metadata': metadata,
    };
  }

  /// 复制并修改部分属性
  ChatMessage copyWith({
    String? id,
    String? content,
    bool? isFromUser,
    DateTime? timestamp,
    String? meetingRecordId,
    ChatMessageStatus? status,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      isFromUser: isFromUser ?? this.isFromUser,
      timestamp: timestamp ?? this.timestamp,
      meetingRecordId: meetingRecordId ?? this.meetingRecordId,
      status: status ?? this.status,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'ChatMessage(id: $id, isFromUser: $isFromUser, content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 聊天消息状态枚举
enum ChatMessageStatus {
  sending('sending'),
  sent('sent'),
  error('error');

  const ChatMessageStatus(this.value);
  final String value;

  static ChatMessageStatus fromString(String value) {
    return ChatMessageStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => ChatMessageStatus.sent,
    );
  }
}

/// 聊天会话模型
class ChatSession {
  final String id;
  final String? meetingRecordId;
  final String title;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<ChatMessage> messages;
  final Map<String, dynamic>? metadata;

  const ChatSession({
    required this.id,
    this.meetingRecordId,
    required this.title,
    required this.createdAt,
    required this.updatedAt,
    this.messages = const [],
    this.metadata,
  });

  /// 创建新的聊天会话
  factory ChatSession.create({
    String? meetingRecordId,
    required String title,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    return ChatSession(
      id: const Uuid().v4(),
      meetingRecordId: meetingRecordId,
      title: title,
      createdAt: now,
      updatedAt: now,
      messages: [],
      metadata: metadata,
    );
  }

  /// 从JSON创建会话
  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      id: json['id'] as String,
      meetingRecordId: json['meeting_record_id'] as String?,
      title: json['title'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      messages: (json['messages'] as List<dynamic>?)
          ?.map((m) => ChatMessage.fromJson(m as Map<String, dynamic>))
          .toList() ?? [],
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'meeting_record_id': meetingRecordId,
      'title': title,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'messages': messages.map((m) => m.toJson()).toList(),
      'metadata': metadata,
    };
  }

  /// 复制并修改部分属性
  ChatSession copyWith({
    String? id,
    String? meetingRecordId,
    String? title,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<ChatMessage>? messages,
    Map<String, dynamic>? metadata,
  }) {
    return ChatSession(
      id: id ?? this.id,
      meetingRecordId: meetingRecordId ?? this.meetingRecordId,
      title: title ?? this.title,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      messages: messages ?? this.messages,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 添加消息
  ChatSession addMessage(ChatMessage message) {
    final updatedMessages = List<ChatMessage>.from(messages)..add(message);
    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }

  /// 更新最后一条消息
  ChatSession updateLastMessage(ChatMessage message) {
    if (messages.isEmpty) {
      return addMessage(message);
    }
    
    final updatedMessages = List<ChatMessage>.from(messages);
    updatedMessages[updatedMessages.length - 1] = message;
    
    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }

  /// 获取最后一条消息
  ChatMessage? get lastMessage => messages.isNotEmpty ? messages.last : null;

  /// 获取消息数量
  int get messageCount => messages.length;

  @override
  String toString() {
    return 'ChatSession(id: $id, title: $title, messageCount: $messageCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatSession && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// LLM配置模型
class LLMConfig {
  final String provider;
  final String model;
  final double temperature;
  final int maxTokens;
  final double? topP;
  final double? frequencyPenalty;
  final double? presencePenalty;
  final Map<String, dynamic>? additionalParams;

  const LLMConfig({
    required this.provider,
    required this.model,
    this.temperature = 0.7,
    this.maxTokens = 2000,
    this.topP,
    this.frequencyPenalty,
    this.presencePenalty,
    this.additionalParams,
  });

  /// 默认配置
  factory LLMConfig.defaultConfig() {
    return const LLMConfig(
      provider: 'deepseek',
      model: 'deepseek-chat',
      temperature: 0.7,
      maxTokens: 2000,
    );
  }

  /// 从JSON创建配置
  factory LLMConfig.fromJson(Map<String, dynamic> json) {
    return LLMConfig(
      provider: json['provider'] as String,
      model: json['model'] as String,
      temperature: (json['temperature'] as num?)?.toDouble() ?? 0.7,
      maxTokens: json['max_tokens'] as int? ?? 2000,
      topP: (json['top_p'] as num?)?.toDouble(),
      frequencyPenalty: (json['frequency_penalty'] as num?)?.toDouble(),
      presencePenalty: (json['presence_penalty'] as num?)?.toDouble(),
      additionalParams: json['additional_params'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'provider': provider,
      'model': model,
      'temperature': temperature,
      'max_tokens': maxTokens,
      if (topP != null) 'top_p': topP,
      if (frequencyPenalty != null) 'frequency_penalty': frequencyPenalty,
      if (presencePenalty != null) 'presence_penalty': presencePenalty,
      if (additionalParams != null) 'additional_params': additionalParams,
    };
  }
}
