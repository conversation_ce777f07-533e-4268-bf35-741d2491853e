// Copyright (c)  2024  Xiaomi Corporation
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:record/record.dart';
import '../model/asr_state.dart';

/// 音频录制服务
/// 
/// 封装record插件的调用，处理麦克风权限、录音控制等
class AudioService {
  AudioService() : _audioRecorder = AudioRecorder();

  final AudioRecorder _audioRecorder;
  StreamSubscription<RecordState>? _recordSub;
  StreamSubscription<List<int>>? _audioStreamSub;

  /// 当前录音状态
  RecordState get recordState => _recordState;
  RecordState _recordState = RecordState.stop;

  /// 录音状态变化流
  Stream<RecordState> get onStateChanged => _audioRecorder.onStateChanged();

  /// 初始化音频服务
  Future<void> initialize() async {
    _recordSub = _audioRecorder.onStateChanged().listen((recordState) {
      _recordState = recordState;
    });
  }

  /// 检查是否有录音权限
  Future<bool> hasPermission() async {
    return await _audioRecorder.hasPermission();
  }

  /// 检查编码器是否支持
  Future<bool> isEncoderSupported(AudioEncoder encoder) async {
    final isSupported = await _audioRecorder.isEncoderSupported(encoder);

    if (!isSupported) {
      debugPrint('${encoder.name} is not supported on this platform.');
      debugPrint('Supported encoders are:');

      for (final e in AudioEncoder.values) {
        if (await _audioRecorder.isEncoderSupported(e)) {
          debugPrint('- ${e.name}');
        }
      }
    }

    return isSupported;
  }

  /// 获取输入设备列表
  Future<List<InputDevice>> listInputDevices() async {
    return await _audioRecorder.listInputDevices();
  }

  /// 开始录音流
  ///
  /// [config] ASR配置
  /// [onAudioData] 音频数据回调
  ///
  /// 返回是否成功开始录音
  Future<bool> startStream({
    required AsrConfig config,
    required Function(List<int>) onAudioData,
  }) async {
    try {
      // 检查权限
      if (!await hasPermission()) {
        debugPrint('No microphone permission');
        return false;
      }

      // 检查编码器支持
      const encoder = AudioEncoder.pcm16bits;
      if (!await isEncoderSupported(encoder)) {
        debugPrint('PCM16 encoder not supported');
        return false;
      }

      // 配置录音参数
      final recordConfig = RecordConfig(
        encoder: encoder,
        sampleRate: config.sampleRate,
        numChannels: config.numChannels,
      );

      // 开始录音流
      final stream = await _audioRecorder.startStream(recordConfig);
      
      // 监听音频数据
      _audioStreamSub = stream.listen(
        (data) {
          onAudioData(data);
        },
        onDone: () {
          debugPrint('Audio stream stopped.');
        },
        onError: (error) {
          debugPrint('Audio stream error: $error');
        },
      );

      return true;
    } catch (e) {
      debugPrint('Failed to start audio stream: $e');
      return false;
    }
  }

  /// 停止录音
  Future<void> stop() async {
    try {
      await _audioStreamSub?.cancel();
      _audioStreamSub = null;
      await _audioRecorder.stop();
    } catch (e) {
      debugPrint('Failed to stop audio recording: $e');
    }
  }

  /// 暂停录音
  Future<void> pause() async {
    try {
      await _audioRecorder.pause();
    } catch (e) {
      debugPrint('Failed to pause audio recording: $e');
    }
  }

  /// 恢复录音
  Future<void> resume() async {
    try {
      await _audioRecorder.resume();
    } catch (e) {
      debugPrint('Failed to resume audio recording: $e');
    }
  }

  /// 释放资源
  void dispose() {
    _recordSub?.cancel();
    _audioStreamSub?.cancel();
    _audioRecorder.dispose();
  }
}
