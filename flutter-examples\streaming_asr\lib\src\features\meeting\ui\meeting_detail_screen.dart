// Copyright (c) 2024 VocalMind AI
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';

import '../../../core/router/app_router.dart';
import '../../../core/utils/date_utils.dart' as app_date_utils;
import '../../../shared/ui/custom_button.dart';
import '../../../shared/ui/loading_indicator.dart';
import '../controller/meeting_controller.dart';
import '../model/meeting_models.dart';

/// 会议记录详情页面
class MeetingDetailScreen extends ConsumerStatefulWidget {
  final String meetingId;
  
  const MeetingDetailScreen({
    super.key,
    required this.meetingId,
  });

  @override
  ConsumerState<MeetingDetailScreen> createState() => _MeetingDetailScreenState();
}

class _MeetingDetailScreenState extends ConsumerState<MeetingDetailScreen> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  final TextEditingController _summaryController = TextEditingController();
  final TextEditingController _tagController = TextEditingController();
  
  bool _isEditing = false;
  MeetingRecord? _originalMeeting;
  List<String> _tags = [];

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _summaryController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return FutureBuilder<MeetingRecord?>(
      future: ref.read(meetingControllerProvider.notifier).getMeetingById(widget.meetingId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('会议详情'),
              backgroundColor: theme.colorScheme.inversePrimary,
            ),
            body: const Center(child: CustomLoadingIndicator()),
          );
        }
        
        if (snapshot.hasError || !snapshot.hasData) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('会议详情'),
              backgroundColor: theme.colorScheme.inversePrimary,
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: theme.colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '会议记录不存在或已被删除',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  CustomButton(
                    text: '返回',
                    onPressed: () => context.pop(),
                  ),
                ],
              ),
            ),
          );
        }
        
        final meeting = snapshot.data!;
        
        // 初始化控制器内容（仅在第一次加载时）
        if (_originalMeeting == null) {
          _originalMeeting = meeting;
          _titleController.text = meeting.title;
          _contentController.text = meeting.originalContent;
          _summaryController.text = meeting.summary ?? '';
          _tags = List.from(meeting.tags);
        }
        
        return _buildDetailScreen(context, theme, meeting);
      },
    );
  }

  Widget _buildDetailScreen(BuildContext context, ThemeData theme, MeetingRecord meeting) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? '编辑会议' : '会议详情'),
        backgroundColor: theme.colorScheme.inversePrimary,
        actions: [
          if (!_isEditing) ...[
            // 分享按钮
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () => _shareMeeting(meeting),
            ),
            // 编辑按钮
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _toggleEditMode(),
            ),
            // 删除按钮
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteDialog(context, meeting),
            ),
          ] else ...[
            // 取消按钮
            TextButton(
              onPressed: _cancelEdit,
              child: const Text('取消'),
            ),
            // 保存按钮
            TextButton(
              onPressed: () => _saveMeeting(meeting),
              child: const Text('保存'),
            ),
          ],
        ],
      ),
      body: _buildBody(theme, meeting),
      floatingActionButton: !_isEditing ? FloatingActionButton(
        onPressed: () => _navigateToAiChat(meeting),
        child: const Icon(Icons.chat),
        tooltip: 'AI 对话',
      ) : null,
    );
  }

  Widget _buildBody(ThemeData theme, MeetingRecord meeting) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 会议信息卡片
          _buildInfoCard(theme, meeting),
          const SizedBox(height: 16),
          
          // 标题部分
          _buildTitleSection(theme),
          const SizedBox(height: 16),
          
          // 内容部分
          _buildContentSection(theme),
          const SizedBox(height: 16),
          
          // 摘要部分
          _buildSummarySection(theme),
          const SizedBox(height: 16),
          
          // 标签部分
          _buildTagsSection(theme),
          const SizedBox(height: 80), // 为FAB留出空间
        ],
      ),
    );
  }

  Widget _buildInfoCard(ThemeData theme, MeetingRecord meeting) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  '会议信息',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow('创建时间', app_date_utils.DateUtils.formatDateTime(meeting.createdAt)),
            _buildInfoRow('更新时间', app_date_utils.DateUtils.formatDateTime(meeting.updatedAt)),
            _buildInfoRow('录音时长', meeting.formattedDuration),
            _buildInfoRow('字数统计', '${meeting.wordCount} 字'),
            _buildInfoRow('说话人数', '${meeting.speakerCount} 人'),
            _buildInfoRow('状态', _getStatusText(meeting.status)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.title, color: theme.colorScheme.primary),
            const SizedBox(width: 8),
            Text(
              '会议标题',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _titleController,
          enabled: _isEditing,
          decoration: InputDecoration(
            hintText: '请输入会议标题',
            border: _isEditing ? const OutlineInputBorder() : InputBorder.none,
            filled: !_isEditing,
            fillColor: _isEditing ? null : Colors.grey[100],
          ),
          style: theme.textTheme.titleLarge,
        ),
      ],
    );
  }

  Widget _buildContentSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.article, color: theme.colorScheme.primary),
            const SizedBox(width: 8),
            Text(
              '会议内容',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            if (!_isEditing)
              IconButton(
                icon: const Icon(Icons.copy),
                onPressed: () => _copyToClipboard(_contentController.text),
                tooltip: '复制内容',
              ),
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _contentController,
          enabled: _isEditing,
          maxLines: _isEditing ? 10 : null,
          decoration: InputDecoration(
            hintText: '会议内容将在这里显示',
            border: _isEditing ? const OutlineInputBorder() : InputBorder.none,
            filled: !_isEditing,
            fillColor: _isEditing ? null : Colors.grey[100],
          ),
        ),
      ],
    );
  }

  Widget _buildSummarySection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.summarize, color: theme.colorScheme.primary),
            const SizedBox(width: 8),
            Text(
              '会议摘要',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            if (!_isEditing && _summaryController.text.isNotEmpty)
              IconButton(
                icon: const Icon(Icons.copy),
                onPressed: () => _copyToClipboard(_summaryController.text),
                tooltip: '复制摘要',
              ),
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _summaryController,
          enabled: _isEditing,
          maxLines: _isEditing ? 5 : null,
          decoration: InputDecoration(
            hintText: '暂无摘要，可以通过AI生成',
            border: _isEditing ? const OutlineInputBorder() : InputBorder.none,
            filled: !_isEditing,
            fillColor: _isEditing ? null : Colors.grey[100],
          ),
        ),
      ],
    );
  }

  Widget _buildTagsSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.label, color: theme.colorScheme.primary),
            const SizedBox(width: 8),
            Text(
              '标签',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            if (_isEditing)
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: _showAddTagDialog,
                tooltip: '添加标签',
              ),
          ],
        ),
        const SizedBox(height: 8),
        if (_tags.isEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '暂无标签',
              style: TextStyle(
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          )
        else
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _tags.map((tag) => _buildTagChip(theme, tag)).toList(),
          ),
      ],
    );
  }

  Widget _buildTagChip(ThemeData theme, String tag) {
    return Chip(
      label: Text(tag),
      deleteIcon: _isEditing ? const Icon(Icons.close, size: 18) : null,
      onDeleted: _isEditing ? () => _removeTag(tag) : null,
      backgroundColor: theme.colorScheme.primaryContainer,
      labelStyle: TextStyle(
        color: theme.colorScheme.onPrimaryContainer,
      ),
    );
  }

  // 辅助方法
  String _getStatusText(MeetingStatus status) {
    switch (status) {
      case MeetingStatus.draft:
        return '草稿';
      case MeetingStatus.processing:
        return '处理中';
      case MeetingStatus.completed:
        return '已完成';
      case MeetingStatus.archived:
        return '已归档';
    }
  }

  void _toggleEditMode() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  void _cancelEdit() {
    if (_originalMeeting != null) {
      _titleController.text = _originalMeeting!.title;
      _contentController.text = _originalMeeting!.originalContent;
      _summaryController.text = _originalMeeting!.summary ?? '';
      _tags = List.from(_originalMeeting!.tags);
    }
    setState(() {
      _isEditing = false;
    });
  }

  Future<void> _saveMeeting(MeetingRecord meeting) async {
    final updatedMeeting = meeting.copyWith(
      title: _titleController.text.trim(),
      originalContent: _contentController.text.trim(),
      summary: _summaryController.text.trim().isEmpty ? null : _summaryController.text.trim(),
      tags: _tags,
      wordCount: _contentController.text.trim().length,
    );

    try {
      await ref.read(meetingControllerProvider.notifier).updateMeeting(updatedMeeting);
      _originalMeeting = updatedMeeting;
      setState(() {
        _isEditing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('会议记录已保存')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失败: $e')),
        );
      }
    }
  }

  Future<void> _copyToClipboard(String text) async {
    await Clipboard.setData(ClipboardData(text: text));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('已复制到剪贴板')),
      );
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  void _showAddTagDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加标签'),
        content: TextField(
          controller: _tagController,
          decoration: const InputDecoration(
            hintText: '请输入标签名称',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final tag = _tagController.text.trim();
              if (tag.isNotEmpty && !_tags.contains(tag)) {
                setState(() {
                  _tags.add(tag);
                });
                _tagController.clear();
              }
              Navigator.of(context).pop();
            },
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }

  Future<void> _shareMeeting(MeetingRecord meeting) async {
    final content = '''
会议标题: ${meeting.title}
创建时间: ${app_date_utils.DateUtils.formatDateTime(meeting.createdAt)}
录音时长: ${meeting.formattedDuration}

会议内容:
${meeting.originalContent}

${meeting.summary?.isNotEmpty == true ? '会议摘要:\n${meeting.summary}\n\n' : ''}${meeting.tags.isNotEmpty ? '标签: ${meeting.tags.join(', ')}' : ''}
''';

    await Share.share(content);
  }

  void _showDeleteDialog(BuildContext context, MeetingRecord meeting) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除会议记录'),
        content: Text('确定要删除会议记录"${meeting.title}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              final goRouter = GoRouter.of(context);

              navigator.pop();
              try {
                await ref.read(meetingControllerProvider.notifier).deleteMeeting(meeting.id);
                if (mounted) {
                  goRouter.pop(); // 返回到列表页面
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(content: Text('会议记录已删除')),
                  );
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(content: Text('删除失败: $e')),
                  );
                }
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _navigateToAiChat(MeetingRecord meeting) {
    // TODO: 导航到AI聊天页面，传递会议内容作为上下文
    context.go('${AppRoutes.aiChat}?meetingId=${meeting.id}');
  }
}
