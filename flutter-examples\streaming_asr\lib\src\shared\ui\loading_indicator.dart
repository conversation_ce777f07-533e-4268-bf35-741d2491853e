// Copyright (c) 2024 VocalMind AI
import 'package:flutter/material.dart';

/// 加载指示器样式枚举
enum LoadingIndicatorStyle {
  circular,
  linear,
  dots,
  pulse,
}

/// 加载指示器大小枚举
enum LoadingIndicatorSize {
  small,
  medium,
  large,
}

/// 自定义加载指示器组件
class CustomLoadingIndicator extends StatelessWidget {
  const CustomLoadingIndicator({
    super.key,
    this.style = LoadingIndicatorStyle.circular,
    this.size = LoadingIndicatorSize.medium,
    this.color,
    this.message,
    this.showBackground = false,
  });

  final LoadingIndicatorStyle style;
  final LoadingIndicatorSize size;
  final Color? color;
  final String? message;
  final bool showBackground;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final indicatorColor = color ?? theme.colorScheme.primary;
    
    Widget indicator = _buildIndicator(indicatorColor);
    
    if (message != null) {
      indicator = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          indicator,
          const SizedBox(height: 16),
          Text(
            message!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }
    
    if (showBackground) {
      return Container(
        color: Colors.black.withOpacity(0.3),
        child: Center(child: indicator),
      );
    }
    
    return indicator;
  }

  Widget _buildIndicator(Color color) {
    switch (style) {
      case LoadingIndicatorStyle.circular:
        return SizedBox(
          width: _getSize(),
          height: _getSize(),
          child: CircularProgressIndicator(
            strokeWidth: _getStrokeWidth(),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        );
        
      case LoadingIndicatorStyle.linear:
        return SizedBox(
          width: _getLinearWidth(),
          child: LinearProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        );
        
      case LoadingIndicatorStyle.dots:
        return _DotsLoadingIndicator(
          color: color,
          size: _getDotSize(),
        );
        
      case LoadingIndicatorStyle.pulse:
        return _PulseLoadingIndicator(
          color: color,
          size: _getSize(),
        );
    }
  }

  double _getSize() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 20;
      case LoadingIndicatorSize.medium:
        return 32;
      case LoadingIndicatorSize.large:
        return 48;
    }
  }

  double _getStrokeWidth() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 2;
      case LoadingIndicatorSize.medium:
        return 3;
      case LoadingIndicatorSize.large:
        return 4;
    }
  }

  double _getLinearWidth() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 100;
      case LoadingIndicatorSize.medium:
        return 150;
      case LoadingIndicatorSize.large:
        return 200;
    }
  }

  double _getDotSize() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 6;
      case LoadingIndicatorSize.medium:
        return 8;
      case LoadingIndicatorSize.large:
        return 12;
    }
  }
}

/// 点状加载指示器
class _DotsLoadingIndicator extends StatefulWidget {
  const _DotsLoadingIndicator({
    required this.color,
    required this.size,
  });

  final Color color;
  final double size;

  @override
  State<_DotsLoadingIndicator> createState() => _DotsLoadingIndicatorState();
}

class _DotsLoadingIndicatorState extends State<_DotsLoadingIndicator>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(3, (index) {
      return AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      );
    });

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    _startAnimations();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          _controllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: widget.size * 0.2),
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                color: widget.color.withOpacity(0.3 + 0.7 * _animations[index].value),
                shape: BoxShape.circle,
              ),
            );
          },
        );
      }),
    );
  }
}

/// 脉冲加载指示器
class _PulseLoadingIndicator extends StatefulWidget {
  const _PulseLoadingIndicator({
    required this.color,
    required this.size,
  });

  final Color color;
  final double size;

  @override
  State<_PulseLoadingIndicator> createState() => _PulseLoadingIndicatorState();
}

class _PulseLoadingIndicatorState extends State<_PulseLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _opacityAnimation = Tween<double>(begin: 1.0, end: 0.3).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                color: widget.color,
                shape: BoxShape.circle,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// 全屏加载遮罩
class LoadingOverlay extends StatelessWidget {
  const LoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.message,
    this.style = LoadingIndicatorStyle.circular,
    this.size = LoadingIndicatorSize.medium,
  });

  final bool isLoading;
  final Widget child;
  final String? message;
  final LoadingIndicatorStyle style;
  final LoadingIndicatorSize size;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Positioned.fill(
            child: CustomLoadingIndicator(
              style: style,
              size: size,
              message: message,
              showBackground: true,
            ),
          ),
      ],
    );
  }
}
